# frozen_string_literal: true

ActiveAdmin.register_page "Dashboard" do
  menu priority: 2, label: proc { I18n.t("active_admin.dashboard") }

  content title: proc { I18n.t("active_admin.dashboard") } do
    data = Admin::Dashboard.data

    div class: "sm:flex gap-4" do
      div class: "w-full" do
        panel "Sales (WTD)" do
          para do
            "Orders: #{data[:shopify_order_count]}"
          end
          para do
            "AOV: #{number_to_currency(data[:shopify_aov])}"
          end
          br
          br
          para do
            "<b>Total</b>: #{number_to_currency(data[:shopify_sales])}".html_safe
          end
        end
      end

      div class: "w-full" do
        panel "Marketing" do
          para do
            "Referral Partners: #{data[:referral_partners]}"
          end
          para do
            "Plus Subscribers: #{data[:plus_subscribers]}"
          end
        end
      end

      div class: "w-full" do
        panel "Plugins" do
          para do
            "Native: #{data[:native_plugin_count]}"
          end
          para do
            "Private: #{data[:private_plugin_count]}"
          end
          para do
            "Third party: #{data[:third_party_plugin_count]}"
          end
          para do
            "Recipes: #{data[:recipe_count]}"
          end
          para do
            "Connections: #{data[:plugin_setting_count]}"
          end
        end
      end

      div class: "w-full" do
        panel "Playlists" do
          para do
            "Regular: #{data[:regular_playlist_count]}"
          end
          para do
            "Mashup: #{data[:mashup_count]}"
          end
          para do
            "Avg per device: #{(data[:playlists_count].to_f / data[:device_setup_completed]).round(2)}"
          end
          br
          para do
            "<b>Total</b>: #{data[:playlists_count]} items".html_safe
          end
        end
      end

      div class: "w-full" do
        panel "Devices" do
          para do
            "Physical: #{number_with_delimiter(data[:distributed_device_count])}"
          end
          para do
            "Virtual: #{number_with_delimiter(data[:virtual_device_count])}"
          end
          para do
            "Setup: #{number_with_delimiter(data[:device_setup_completed])}"
          end
          para do
            "Active: #{number_with_delimiter(data[:device_active])}"
          end
          para do
            "Alive: #{number_with_delimiter(data[:device_alive])}"
          end
        end
      end
    end

    div class: "md:grid md:grid-cols-3 gap-4" do
      div do
        panel "User Registrations" do
          line_chart data[:users], curve: true, points: false, colors: ['black']
        end
      end

      div do
        panel "Plugin Connections" do
          line_chart data[:plugin_connections], curve: true, points: false, colors: ['#ff9280']
        end
      end

      div do
        panel "Device Activations" do
          line_chart data[:device_activations], curve: true, points: false, colors: ['black']
        end
      end
    end

    div do
      panel "Connections by Plugin (Top 10)" do
        column_chart data[:top_connections_by_plugin], colors: ['black', '#ff9280', '#33a398']
      end
    end

    div do
      panel "Connections by Plugin (Rank 11+)" do
        column_chart data[:bottom_connections_by_plugin], colors: ['black', '#ff9280', '#33a398']
      end
    end
  end
end
