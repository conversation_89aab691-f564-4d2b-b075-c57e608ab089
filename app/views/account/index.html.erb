<% meta title: t('.title') %>

<div class="<%= layout_wide_classes %>">
  <div class="<%= layout_title_classes %>">
    <div class="w-full my-4">
      <div class="flex mb-4">
        <div class="flex flex-grow items-center">
          <div class="flex flex-col">
            <h2 class="<%= title_h2_classes %>"><%= t('.title') %></h2>
            <p class="<%= title_p_classes %>"><%= t('.tagline') %></p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="<%= layout_double_col_classes %> !px-0 !gap-0">

      <div class="<%= layout_single_col_classes %> !justify-start !px-4">
        <div class="<%= card_classes %> divide-y divide-gray-200 dark:divide-gray-700">
          <div class="p-6">
            <label class="<%= field_label_classes %>"><%= t('.refer_and_earn') %></label>
            <p class="<%= field_description_classes %> mb-2"><%= t('.refer_and_earn_hint') %></p>

            <% if current_user.referral_partner? %>
              <%= render "shared/click_to_copy", label: '', object_id: current_user.id, value: "https://usetrmnl.com?ref=#{current_user.referral_code}", type: 'referralCode' %>
              <p class="<%= field_description_classes %>"><%= t('.refer_and_earn_redemptions') %>: <span id="referral_code_redemptions"><%= t(:fetching) %>...</span></p>

              <script type="text/javascript">
                fetch("/referral_code.json")
                .then(function(response) { return response.json() })
                .then(function(json) {
                  document.getElementById('referral_code_redemptions').innerText = json.usage_count;
                });
              </script>
            <% else %>
              <%= button_to referral_code_index_path, data: { turbo_submits_with: 'Please wait...' }, class: "#{button_regular_classes} #{button_primary_classes}" do %>
                <%= t('.refer_and_earn_request_code') %>
              <% end %>
            <% end %>
          </div>
        </div>

        <% unless current_user.hackathon_user? %>
          <div class="<%= card_classes %> divide-y divide-gray-200 dark:divide-gray-700">
            <div class="p-6">
              <div class="flex">
                <div>
                  <label for="user_discord_username" class="<%= field_label_classes %>">Discord Community</label>
                </div>
                <div class="text-right flex-1">
                  <% discord_link = current_user.owns_developer_edition_device? ? Rails.application.credentials.discord_server_invite_url : upgrade_index_path %>
                  <%= link_to discord_link, title: "Join our developer-only server", target: :_blank do %>
                    <%= image_tag 'discord-server.png', class: "w-28 ml-auto" %>
                  <% end %>
                </div>
              </div>
              <p class="<%= field_description_classes %>">What's your Discord username?</p>
              <input type="text" name="user[discord_username]" id="user_discord_username" class="<%= field_input_classes %>" value="<%= current_user.discord_username %>" placeholder="solidsnake" form="edit_user_<%= current_user.id %>">
              <small class="<%= field_help_text_classes %>">May be used for support, attribution on published Recipes, etc.</small>
            </div>
          </div>
        <% end %>

        <% if current_user.owns_developer_edition_device? %>
          <div class="<%= card_classes %> divide-y divide-gray-200 dark:divide-gray-700">
            <div class="p-6">
              <label class="<%= field_label_classes %>"><%= t('.api_key') %></label>
              <p class="<%= field_description_classes %> mb-2"><%= t('.api_key_label') %></p>

              <div class="flex items-center mb-2 gap-2">
                <div class="w-full -mb-2">
                  <%= render "shared/click_to_copy", label: '', object_id: current_user.id, value: current_user.api_key || '(none yet)', type: 'apiKey' %>
                </div>
                <%= button_to t('.api_key_reset'), reset_api_key_account_index_path, class: "#{button_regular_classes} #{button_primary_classes}",
                  data: {
                    turbo: false, # turbolinks breaks the multi-clipboard controller
                    controller: 'confirm', # can't use data-turbo-confirm since that is disable too, so use a custom controller
                    confirm_target: 'button',
                    confirm_message_value: t('.api_key_confirm'),
                  }
                %>
              </div>

              <p class="<%= field_description_classes %>">
                <%= string_with_link(t('.api_key_hint'), 'https://help.usetrmnl.com/en/articles/********-user-level-api-keys', class: 'underline', target: '_blank') %>
              </p>
            </div>
          </div>
        <% end %>


          <div class="<%= card_classes %> divide-y divide-gray-200 dark:divide-gray-700">
            <div class="p-6">
              <label class="<%= field_label_classes %>">Beta Features</label>
              <p class="<%= field_description_classes %> mb-2">Try out new stuff here. Options may disappear at any time.</p>

              <% if Flipper.enabled?(:hide_title_bar, current_user) %>
                <label class="inline-flex items-center cursor-pointer my-3">
                  <%= hidden_field_tag 'user[title_bar_enabled]', false, form: "edit_user_#{current_user.id}" %>
                  <%= check_box_tag :title_bar_enabled, 'true', current_user.title_bar_enabled?, { name: 'user[title_bar_enabled]', id: 'user_title_bar_enabled', class: 'sr-only peer', form: "edit_user_#{current_user.id}" } %>
                  <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Show title bar</span>
                </label>
                <p class="<%= field_description_classes %>">If disabled, native (non global) and third party plugins will be more minimal.</p>
              <% end %>

              <div class="mt-8">
                <label class="<%= field_label_classes %>">Playlist Schedules 2.0</label>
                <p class="<%= field_description_classes %> mb-4">Help us improve our new, more advanced playlist scheduling system before it goes public.</p>

                <% if Flipper.enabled?(:playlist_schedules, current_user) %>
                  <div class="<%= field_description_classes %>">You're in! Thanks for being a beta tester.</div>
                <% else %>
                  <%= button_to 'Join Schedule 2.0 Beta', join_schedule_beta_account_index_path, method: :post, class: "#{button_regular_classes} #{button_primary_classes} mb-2", data: { turbo_confirm: 'Are you sure? This cannot be undone.' } %>
                  <div class="<%= field_description_classes %>">This is a one-way data migration and can't be reversed.</div>
                <% end %>
              </div>
            </div>
          </div>

        <% if Flipper.enabled?(:plus_subscription, current_user) %>
          <div class="<%= card_classes %>">
            <div class="p-6">
              <label class="<%= field_label_classes %>">TRMNL+ Subscription</label>

              <% if current_user.subscribed_to_plus? %>
                <p class="<%= field_description_classes %> mb-4">Want to update your payment method or cancel?</p>
                <%= button_to 'Manage', billing_portal_index_path, method: :post, data: { turbo: false }, class: "#{button_regular_classes} #{button_primary_classes}" %>
              <% else %>
                <p class="<%= field_description_classes %> mb-4">Access faster refresh rates, higher rate limits, and more.</p>
                <%= link_to 'Subscribe', plus_path, class: "#{button_regular_classes} #{button_primary_classes}", data: { turbo: false } %>
              <% end %>
            </div>
          </div>
        <% end %>

        <div class="<%= card_classes %> mb-6 divide-y divide-gray-200 dark:divide-gray-700">
          <div class="p-6">
            <label class="<%= field_label_classes %>"><%= t('.session') %></label>
            <p class="<%= field_description_classes %>"><%= t('.session_hint', user: current_user.email) %></p>
            <%= link_to t(:log_out), logout_path, class: "#{button_regular_classes} #{button_primary_classes} mt-4", data: { turbo: false } %>
          </div>
        </div>
      </div>

      <%= form_for(current_user, url: account_path(current_user.id), method: 'patch', html: { autocomplete: 'off' }) do |f| %>
        <div class="<%= layout_single_col_classes %> !px-4">
          <div class="<%= card_classes %> mb-6 divide-y divide-gray-200 dark:divide-gray-700">
            <div class="p-6">
              <label for="user_first_name" class="<%= field_label_classes %>"><%= t(:first_name) %></label>
              <p class="<%= field_description_classes %>"><%= t('.first_name_hint') %></p>
              <input type="text" required="true" name="user[first_name]" id="user_first_name" class="<%= field_input_classes %>" value="<%= current_user.first_name %>" placeholder="Michael">
            </div>
            <div class="p-6">
              <label for="user_last_name" class="<%= field_label_classes %>"><%= t(:last_name) %></label>
              <p class="<%= field_description_classes %>"><%= t('.last_name_hint') %></p>
              <input type="text" required="true" name="user[last_name]" id="user_last_name" class="<%= field_input_classes %>" value="<%= current_user.last_name %>" placeholder="Bloomberg">
            </div>
            <div class="p-6">
              <label for="user_email" class="<%= field_label_classes %>"><%= t('.email_address') %></label>
              <p class="<%= field_description_classes %>"><%= t('.email_address_hint') %></p>
              <input type="text" required="true" name="user[email]" id="user_email" class="<%= field_input_classes %>" value="<%= current_user.email %>" placeholder="<%= t(:email_address) %>">
            </div>

            <div class="p-6">
              <label for="user_password" class="<%= field_label_classes %>"><%= t(:password) %></label>
              <p class="<%= field_description_classes %>"><%= t('.password_hint') %></p>
              <input type="password" autocomplete="new-password" name="user[password]" id="user_password" class="<%= field_input_classes %>" placeholder="· · · · · · · · · · · · · ">
            </div>

            <% if current_user.otp_required_for_login? %>
              <div class="p-6">
                <label for="user_otp_attempt" class="<%= field_label_classes %>"><%= t('.disable_2fa') %></label>
                <p class="<%= field_description_classes %>"><%= t('.disable_2fa_hint') %></p>
                <input type="text" name="user[otp_attempt]" id="user_otp_attempt" class="<%= field_input_classes %>" placeholder="696420">
              </div>
            <% else %>
              <div class="p-6">
                <label class="<%= field_label_classes %>"><%= t('.enable_2fa') %></label>
                <p class="<%= field_description_classes %>">
                  <%= string_with_link(t('.enable_2fa_hint'), enable_otp_path, id: 'enable_mfa', class: 'underline') %>
                </p>
              </div>
            <% end %>

            <div id="time_zone" class="p-6">
              <label for="user_tz" class="<%= field_label_classes %>"><%= t('.time_zone') %></label>
              <p class="<%= field_description_classes %>"><%= t('.time_zone_hint') %></p>
              <%= f.time_zone_select :tz, nil, {}, { class: field_input_classes } %>
            </div>

            <div class="p-6">
              <label for="user_locale" class="<%= field_label_classes %>"><%= t('.locale') %></label>
              <p class="<%= field_description_classes %>"><%= string_with_link(t('.locale_hint'), 'https://en.wikipedia.org/wiki/List_of_ISO_639_language_codes', { class: 'underline', target: :_blank }) %></p>
              <%= f.select :locale, locale_options, {}, { class: field_input_classes } %>
            </div>

            <div class="p-6 text-right">
              <%= f.submit t(:save), data: { turbo: false }, class: "#{button_regular_classes} #{button_primary_classes}" %>
            </div>
          </div>
        </div>
      <% end %>
  </div>
</div>
