<div class="layout layout--top">
  <div class="columns">
    <div class="column">
      <div class="list">
        <% formulate_and_group_events_by_day(events, today_in_tz, 20, date_format).to_h.each_pair do |day, events| %>
          <span class="label label--small label--inverted"><%== day %></span>

          <% all_day_events = events.filter { it[:all_day] } %>
          <% if all_day_events.present? %>
            <div class="item">
              <div class="meta px--6">
                <span class="text-stroke text-stroke--small">#</span>
              </div>
              <div class="content">
                <span class="title title--small clamp--3"><%= all_day_events.map { it[:summary] }.join(', ') %></span>
              </div>
            </div>
          <% end %>

          <% (events - all_day_events).each do |event| %>
            <div class="flex flex--row w--full">
              <% if include_event_time %>
                <span class="label no-shrink bg-gray-6 bg--gray-60 text--truncate-none w--14 text--center"><%= event[:start].split(' ')[0] %></span>
              <% end %>

              <!-- <div class="flex flex--row w--full stretch flex--left"> -->
              <div class="stretch">
                <span class="value value--xxsmall no-shrink"><%= event[:summary] %></span>

                <% if include_description %>
                  <span class="label text--truncate-1 shrink"><%== event[:description].html_safe %></span>
                <% end %>
              </div>

              <% if include_event_time %>
                <span class="label no-shrink bg-gray-6 bg--gray-60 text--truncate-none w--14 text--center"><%= event[:end].split(' ')[0] %></span>
              <% end %>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
   </div>
</div>
