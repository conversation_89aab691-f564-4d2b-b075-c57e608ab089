<div class="relative overflow-hidden">
  <div class="absolute inset-0 z-0">
    <%= image_tag 'byod.jpg', class: 'absolute inset-0 w-full h-full object-cover bg-right' %>
  </div>
  <div class="container mx-auto max-w-9xl px-8 md:px-20 xl:px-32 py-24 lg:py-32 relative z-10">
    <div class="w-full">
      <div class="flex flex-wrap items-center justify-between">
        <div class="w-full sm:w-2/3 max-w-xl flex flex-col">
          <%= render 'shared/section_heading',
            title: 'Build Your Own Hardware',
            description: 'If you\'d rather roll your own e-ink hardware, BYOD is for you. Combine our open-source firmware with your custom PCB or off-the-shelf microcontroller to create a TRMNL-like experience. It\'s not the most cost-effective route, but it\'s the ultimate way to scratch that DIY itch and learn the inner workings of e-ink dashboards.'
          %>
        </div>
      </div>
    </div>
  </div>
  <% byod_content = capture do %>
    <%
      ecosystem_cards = [
        {
          title: "BYOD",
          tag: "Bring Your Own Device",
          description: "Build your own device with our modified firmware connecting to our servers. While not as cost-effective as buying directly, it's perfect for those who want custom hardware with our software stack.",
          price: "$50 one-time fee",
          link: "https://shop.usetrmnl.com/products/byod",
          button_text: "Get Started",
          icon: "byod",
          features: [
            "Full access to TRMNL web app",
            "Plugin API access for DIY devices",
            "Compatible with multiple microcontrollers",
            "Works with various e-ink display sizes"
          ]
        },
        {
          title: "BYOD/S",
          tag: "Full DIY Stack",
          description: "Bring your own device AND server. Use our open source firmware as a starting point, but run everything on your infrastructure with complete control and privacy.",
          link: "https://docs.usetrmnl.com/go/diy/byod-s",
          button_text: "Documentation",
          icon: "byods",
          features: [
            "Total end-to-end ownership",
            "Maximum privacy and security",
            "Highly customizable server implementation",
            "No dependency on TRMNL infrastructure"
          ]
        },
        {
          title: "Firmware",
          tag: "Open Source (GPL-3)",
          description: "Access our full firmware codebase to understand how TRMNL devices work or to modify for your own projects. Available under MIT license.",
          link: "https://github.com/usetrmnl/firmware",
          button_text: "GitHub Repo",
          icon: "firmware",
          features: [
            "ESP32 and ESP8266 support",
            "E-ink display drivers",
            "Low-power optimization",
            "Over-the-air update system"
          ]
        },
        {
          title: "RPi TRMNL",
          tag: "Ready-Made Solution",
          description: "Turn any Raspberry Pi into a TRMNL with our simple utility. Just run our installer script to get up and running quickly.",
          link: "https://usetrmnl.com/blog/rpi-trmnl",
          button_text: "Learn More",
          icon: "rpi",
          features: [
            "Auto-detects RPi architecture",
            "Simple installation process",
            "Dark mode support",
            "Works with various HDMI displays"
          ]
        },
        {
          title: "Kindle TRMNL",
          tag: "Jailbreak & Dashboard Guide",
          description: "Turn your Amazon Kindle into a TRMNL dashboard. Our guide shows you how to jailbreak with WinterBreak, install TRMNL, and repurpose your device's screen into a powerful dashboard.",
          link: "https://usetrmnl.com/guides/turn-your-amazon-kindle-into-a-trmnl",
          button_text: "Full Kindle Guide",
          icon: "kindle",
          features: [
            "Runs on 10th & 12th gen e-ink Kindles",
            "Detailed jailbreak & install walkthrough",
            "Reduce e-waste: repurpose old Kindle hardware",
            "Open source, community-improvable guide"
          ]
        },
        {
          title: "Nook eReader",
          tag: "Jailbreak & Dashboard Guide",
          description: "Turn your B&N Nook into a TRMNL dashboard. Our guide shows you how to jailbreak with ABD (Android Debug Bridge) and F-Droid, an OSS app store.",
          link: "https://github.com/usetrmnl/trmnl-nook",
          button_text: "Full Nook Guide",
          icon: "nook",
          features: [
            "Runs on Nook Glowlight 4 (others TBD)",
            "Detailed jailbreak & install walkthrough",
            "Reduce e-waste: repurpose old Nook hardware",
            "Open source, community-improvable guide"
          ]
        },
        {
          title: "Android TRMNL",
          tag: "Jailbreak & Dashboard Guide",
          description: "Turn your Android tablet into a TRMNL dashboard. Ideal for mirroring content from a native or BYOD device.",
          link: "https://github.com/usetrmnl/trmnl-android",
          button_text: "Full Android Guide",
          icon: "android",
          features: [
            "Token-based authentication with core + BYOS",
            "Refresh history and logging",
            "Adaptive refresh rate support",
            "Open source, maintained by senior Android engineer"
          ]
        },
        {
          title: "Kobo TRMNL",
          tag: "Jailbreak & Dashboard Guide",
          description: "Turn your Kobo Reader into a TRMNL dashboard. Runs on Kobo Mini, Clara HD, Aura Second Edition.",
          link: "https://github.com/usetrmnl/trmnl-kobo",
          button_text: "Full Kobo Guide",
          icon: "kobo",
          features: [
            "Dual-use with Kobo's native functionality",
            "Does not require a TRMNL license or hardware",
            "Adaptive refresh rate support",
            "Open source, based on popular jailbreak and image parsing libraries"
          ]
        },
        {
          title: "iPad TRMNL",
          tag: "Mirroring Guide",
          description: "Turn your iPad into a TRMNL dashboard. Runs on every generation.",
          link: "https://help.usetrmnl.com/en/articles/11647459-trmnl-for-ipad",
          button_text: "Full iPad Guide",
          icon: "apple",
          features: [
            "Token-based authentication with core + BYOS",
            "Does not require a TRMNL license or hardware",
            "Adaptive refresh rate support"
          ]
        },
        {
          title: "3D Printed Accessories",
          tag: "Free DIY Designs",
          description: "Find community-created and official designs for enclosures and accessories to complete your DIY TRMNL device with a professional finish.",
          link: "https://github.com/usetrmnl/mounts",
          button_text: "Browse Designs",
          icon: "3d",
          features: [
            "Printable case designs",
            "Wall mounting options",
            "Custom stands and holders",
            "Component organization designs"
          ]
        }
      ]
    %>
    <% ecosystem_cards.each do |card| %>
      <div class="flex-shrink-0 w-[calc(100%-2rem)] xs:w-80 sm:w-96">
        <div class="overflow-hidden backdrop-blur-md bg-gray-650/25 rounded-xl h-full flex flex-col">
          <div class="w-full p-4 pb-0 flex flex-row gap-4">
            <div class="w-20 h-20 flex items-center justify-center text-primary-400 mb-4">
              <%= render partial: "shared/icons/#{card[:icon]}", locals: { classes: "h-20 w-20" } rescue nil %>
            </div>
            <div class="flex flex-col justify-center">
              <h3 class="text-2xl font-heading text-primary-400 leading-none"><%= card[:title] %></h3>
              <span class="px-2 py-0.5 text-xs rounded bg-gray-900 text-gray-300 flex w-fit mt-2"><%= card[:tag] %></span>
            </div>
          </div>
          <div class="p-6 flex flex-col flex-grow gap-4">
            <p class="text-gray-300 text-sm"><%= card[:description] %></p>
            <% if card[:price].present? %>
              <div class="bg-primary-800/30 text-primary-400 text-sm font-medium px-4 py-2 rounded-full inline-block mb-4 self-start">
                <%= card[:price] %>
              </div>
            <% end %>
            <% if card[:title] == "RPi TRMNL" %>
              <div class="font-mono text-xs text-gray-100 bg-gray-900/70 p-3 rounded border border-gray-700 mb-4 overflow-x-auto">
                <div class="text-emerald-300 whitespace-nowrap">sudo \curl -sSL https://usetrmnl.com/byod/install-trmnl-display.sh | bash -s</div>
              </div>
            <% end %>
            <div class="mt-auto">
              <ul class="text-sm text-gray-400 space-y-1 mb-4">
                <% card[:features].each do |feature| %>
                  <li class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-primary-400 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    <%= feature %>
                  </li>
                <% end %>
              </ul>
              <a href="<%= card[:link] %>" target="_blank" class="transition w-full flex items-center justify-center text-center text-xs bg-gray-650 hover:bg-gray-600 text-white py-2 px-3 rounded font-medium"><%= card[:button_text] %></a>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  <% end %>
  <%= render 'shared/horizontal_scroll',
    container_id: 'byod-container',
    header_selector: '.font-heading.text-4xl',
    content: byod_content,
    class: 'relative z-10' %>
  <div class="relative z-10 px-8 md:px-20 xl:px-32 max-w-9xl mx-auto mt-12 pb-24 lg:pb-32">
    <%= render 'shared/cta_banner',
      title: "Ready to build your own?",
      description: "Our GitHub repositories contain schematics, firmware, and step-by-step guides for DIY builds.",
      button_text: "GitHub Repo",
      button_url: "https://github.com/usetrmnl/",
      button_color: "bg-primary-500 hover:bg-primary-600",
      target: :_blank
    %>
  </div>
</div>
