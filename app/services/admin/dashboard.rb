module Admin
  class Dashboard
    class << self

      # rubocop:disable Metrics/AbcSize
      def data
        Rails.cache.fetch 'admin_dashboard/v14', expires_in: 1.hour do
          metrics = {}

          ## STAT BOXES ##

          # Sales
          metrics[:referral_partners] = User.referral_partners.count
          metrics[:plus_subscribers] = User.subscribed.count
          metrics[:shopify_order_count] = HTTParty.get("https://usetrmnl.myshopify.com/admin/api/2024-07/orders/count.json?status=any&created_at_min=#{Date.today.beginning_of_week.iso8601}&access_token=#{Rails.application.credentials.shopify_access_token}")['count']

          shopify_sales = 0
          shopify_orders = HTTParty.get("https://usetrmnl.myshopify.com/admin/api/2024-07/orders.json?status=any&created_at_min=#{Date.today.beginning_of_week.iso8601}&fields=created_at,current_subtotal_price,fulfillment_status&access_token=#{Rails.application.credentials.shopify_access_token}&limit=250")
          shopify_sales += shopify_orders['orders']&.sum { |o| o['current_subtotal_price'].to_f }
          more_orders = true

          while more_orders && shopify_orders.headers['link'].present?
            url = shopify_orders.headers['link'].split(', <')[-1].split('>;')[0].delete('<')
            shopify_orders = HTTParty.get("#{url}&access_token=#{Rails.application.credentials.shopify_access_token}&limit=250")
            shopify_sales += shopify_orders['orders']&.sum { |o| o['current_subtotal_price'].to_f }

            more_orders = false unless shopify_orders.headers['link'].include?('next')
          end

          metrics[:shopify_sales] = shopify_sales
          metrics[:shopify_aov] = begin
            metrics[:shopify_sales] / metrics[:shopify_order_count]
          rescue StandardError
            'TBD'
          end

          # Plugins
          metrics[:native_plugin_count] = Plugin.native.count
          metrics[:third_party_plugin_count] = Plugin.third_party.count
          metrics[:private_plugin_count] = PluginSetting.where(plugin_id: Plugin.find_by_keyname('private_plugin')).count
          metrics[:recipe_count] = PluginSetting.recipe.count
          metrics[:plugin_setting_count] = PluginSetting.count

          # Connections
          playlist_items = PlaylistItem.all
          metrics[:regular_playlist_count] = playlist_items.where(mashup_id: nil).count
          metrics[:mashup_count] = playlist_items.where.not(mashup_id: nil).count
          metrics[:playlists_count] = playlist_items.count

          # Devices
          metrics[:virtual_device_count] = Device.virtual.count
          metrics[:distributed_device_count] = Device.physical.count
          metrics[:device_setup_completed] = Device.physical.where('setup_at is not null').count
          metrics[:device_active] = Device.physical.claimed.active.count
          metrics[:device_alive] = Device.physical.claimed.alive.count

          ## CHARTS ##

          # User Registrations
          metrics[:users] = User.group_by_month(:created_at, range: 3.months.ago..Time.now, expand_range: true).count.map { |date, value| [date.strftime("%b"), value] }

          # Plugin Connections
          metrics[:plugin_connections] = PluginSetting.group_by_month(:created_at, range: 3.months.ago..Time.now).count.map { |date, value| [date.strftime("%b"), value] }

          # Device Activations
          metrics[:device_activations] = Device.physical.group_by_month(:setup_at, range: 3.months.ago..Time.now, expand_range: true).count.map { |date, value| [date.strftime("%b"), value] }

          ## ANALYTICS ##

          # Connections by Plugin
          connected_plugins_by_id = PluginSetting.all
                                                 .group(:plugin_id)
                                                 .count
                                                 .select { |_k, v| v > 5 } # only pickup plugins with more than 5 installs.
                                                 .sort_by { |_, v| v }
                                                 .reverse
                                                 .map { |id, qty| [Plugin.find(id).name, qty] }
          metrics[:top_connections_by_plugin] = connected_plugins_by_id[0..9]
          metrics[:bottom_connections_by_plugin] = connected_plugins_by_id[10..]

          metrics
        end
      end
      # rubocop:enable Metrics/AbcSize
    end
  end
end
