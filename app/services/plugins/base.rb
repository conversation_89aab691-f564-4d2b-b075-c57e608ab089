module Plugins
  class Base
    include Helpers::Base

    def initialize(plugin_settings, params = {})
      @plugin_settings = plugin_settings
      @settings = plugin_settings.all_settings
      self.force_refresh = params[:force_refresh] || false
      self.all_models = params[:all_models] || false
    end

    attr_accessor :plugin_settings, :settings, :past_data, :force_refresh, :all_models

    alias force_refresh? force_refresh

    delegate :plugin, :user, :user_id, :id, :image, :model_ids, :refresh_interval, to: :plugin_settings
    delegate :processor, :private?, to: :plugin

    # used by xhr_select and other class-level methods that need access to instance methods
    def self.inst
      new(PluginSetting.new)
    end

    def process!
      log('Beginning plugin refresh')

      update_locals # does not save

      if plugin.skip_screen_generation?
        log('Skipping: this plugin type does not generate screens')
        return save_locals
      end

      if only_in_mashups?
        log('Triggering refresh of mashups')
        process_mashup
        return if plugin_settings.screens.any?
      end

      results = unique_model_ids.map do |model_id|
        process_model(model_id)
      end

      log('Plugin refresh complete')
      results
    rescue DataFetchError => e
      Rails.logger.info "ERROR: Plugins::Base.process! -> DataFetchError -> #{plugin.keyname} #{id} #{e.message}"
    rescue StandardError => e
      Rails.logger.info "ERROR: Plugins::Base.process! -> StandardError: #{plugin.keyname} #{id} #{e}"
      log(e.message, level: :error) if private?
    end

    private

    def process_model(model_id)
      model = DeviceModel.cached_find(model_id)
      model_name = model[:name] || "##{model_id}"

      screen = screen_for(model_id)
      log("Considering screen refresh for #{model_name} model")

      if skip_screen_generation?
        log('Skipping: TRMNL_SKIP_SCREEN_GENERATION == true')
        save_locals
        return
      end

      if skip_display_merge_variable?
        log('Skipping: TRMNL_SKIP_DISPLAY == true')
        plugin_settings.skip_display = true
        save_locals
        return
      else
        plugin_settings.skip_display = false # might be changed later by JS
      end

      if screen.present? && no_change_in_data?(screen)
        log('Skipping: no change in data')
        save_locals
        return
      end

      log('Generating screen now')
      total_start_time = Time.now

      # Create converter
      converter_start = Time.now
      converter = Converter::Html.new({ html: html_document, image:, processor:, plugin: plugin.keyname, id:, model_id: })
      converter_creation_time = Time.now - converter_start

      # Process image
      processing_start = Time.now
      image = converter.process
      processing_time = Time.now - processing_start

      total_elapsed_time = Time.now - total_start_time

      log('Skipping: window.TRMNL_SKIP_DISPLAY == true') if converter.display_skipped
      log('Skipping: window.TRMNL_SKIP_SCREEN_GENERATION == true') if converter.render_skipped

      if image.file?
        upload_start = Time.now
        log("Screen generated in #{total_elapsed_time.round(3)}s (setup: #{converter_creation_time.round(3)}s, processing: #{processing_time.round(3)}s), uploading now")
        screen_for(model_id).upload_image(image.to_s, file_name)
        upload_time = Time.now - upload_start
        log("Upload completed in #{upload_time.round(3)}s")
        increment_daily_usage
      else
        log("No image file generated after #{total_elapsed_time.round(3)}s")
      end

      plugin_settings.skip_display = converter.display_skipped # from JS
      ok = plugin_settings.save
      if ok
        log("Screen refresh for #{model_name} model complete")
      else
        log("Screen refresh failed for #{model_name} model: not saved", level: :error)
      end

      ok
    end

    def process_mashup
      save_locals
      plugin_settings.mashups.each(&:refresh_mashup) if force_refresh? || refreshes_once_per_day
    end

    def save_locals = plugin_settings.save

    def unique_model_ids
      return DeviceModel.all_model_ids if all_models

      model_ids.present? ? model_ids : default_user_model_id
    end

    def default_user_model_id = [plugin_settings.user.current_device.model_id]

    def update_locals
      self.past_data = (plugin_settings.persistence_data['locals'] || {}).sort.to_h

      # don't waste compute generating screens when only a global var has changed
      # caught when plugins/private_plugin_spec -> 'should not process a new image' generated one anyway
      # This is merged back in merged_content
      @global_vars = content.delete(:global_variables) || {}
      plugin_settings.persistence_data['global_variables'] = @global_vars if @global_vars.present?
      plugin_settings.persistence_data['locals'] = content
      plugin_settings.state = :healthy
      plugin_settings.error_retry_count = 0
      plugin_settings.error_message = 0
      increment_daily_usage
    end

    # This avoids processing and creating a new image if the underlying locals content doesn't change.
    def no_change_in_data?(screen = nil)
      return false if force_refresh?
      return false if past_data.nil? || past_data.empty? || content.empty?
      return false if screen && private? && static_strategy? && screen.updated_at.to_i < (DateTime.now - 59.minutes).to_i
      return false if plugin.ignore_persistence_data # some plugins, ex Screenshot, will always have the same content

      past_data.as_json == content.as_json
    end

    def skip_screen_generation? = private? && content.dig(:merge_variables, 'TRMNL_SKIP_SCREEN_GENERATION')
    def skip_display_merge_variable? = private? && content.dig(:merge_variables, 'TRMNL_SKIP_DISPLAY')

    # We want to skip generating an image when plugin_setting is a part of mashup but not playlist.
    def only_in_mashups? = plugin_settings.mashup_contents_count.positive? && !plugin_settings.playlists_count.positive?

    def screen_for(model_id) = Screen.find_or_initialize_by(plugin_setting_id: id, user_id:, model_id: model_id)

    def template_path = "#{self.class.name.gsub('::', '/').underscore}/full"

    def locale = user.locale

    def timezone = user.tz

    def lookback_period = settings['lookback_period'].to_i

    def file_name = "plugin-#{SecureRandom.hex(3)}"

    def refreshes_once_per_day = PluginSetting::REFRESH_OPTIONS['1x /day'] == refresh_interval

    def increment_daily_usage = plugin_settings.daily_usage += 1

    def log(dump, opts = {})
      opts[:level] ||= :debug
      plugin_settings.create_log(dump, opts)
    end
  end
end
