module API
  class FirmwareController < BaseController
    def latest
      render json: { url: stable_release&.url, version: stable_release&.version, merged_path: }
    end

    private

    def stable_release
      Firmware.stable_release
    end

    # used by Provisioner app; flashes boards with latest (merged/uploaded) FW release
    # NOT necessarily the stable release (uploaded only to S3 > trmnl-fw)
    def merged_path
      uploaded_binaries = Dir['public/firmware/trmnl/*']
      latest_release = uploaded_binaries.sort.reverse.first
      latest_release.gsub('public', '')
    end
  end
end
