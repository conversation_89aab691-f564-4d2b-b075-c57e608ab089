class AccountController < ApplicationController
  include Mfable
  before_action :authenticate_user!
  before_action :process_stripe_callback

  def index; end

  def update
    if should_disable_otp?
      attempt_to_disable_otp!
      redirect_to account_index_path
    else
      current_user.update!(account_update_params)
      bypass_sign_in(current_user) # prevents user from needing to log back in
      redirect_to account_index_path, notice: t('.success')
    end
  end

  def stop_impersonating
    stop_impersonating_user
    redirect_to admin_root_path
  end

  def reset_api_key
    current_user.reset_api_key!
    redirect_to account_index_path, notice: t('.success')
  end

  def join_schedule_beta
    return if Flipper.enabled?(:playlist_schedules, current_user)

    current_user.migrate_to_singular_groups

    current_user.flipper_groups << 'schedules_2_beta'
    current_user.save!

    redirect_to account_index_path, notice: 'Playlist Schedules 2.0 enabled - visit the Playlist page to see it in action'
  end

  private

  def account_update_params
    params.require(:user).permit(:first_name, :last_name, :email, :tz, :password, :otp_attempt, :locale, :discord_username, :title_bar_enabled)
  end

  def process_stripe_callback
    flash.now[:notice] = 'Subscription updated successfully' if params[:updated] == 'true'
  end
end
