class BillingPortalController < ApplicationController
  before_action :authenticate_user!
  skip_before_action :verify_authenticity_token, only: [:create] # ajax

  def new
    session = Stripe::Checkout::Session.retrieve(params[:session_id])

    if session.status == 'complete'
      current_user.set_stripe_subscription
      redirect_to dashboard_index_path, notice: 'Your Plus subscription is now active'
    else
      redirect_to plus_path, alert: "Please subscribe to continue."
    end
  end

  def create
    if current_user.subscribed_to_plus?
      redirect_to modify_subscription, allow_other_host: true # /account view
    else
      respond_to do |format|
        format.html { redirect_to plus_path } # /account view
        format.json { render json: create_checkout } # /plus view
      end
    end
  end

  private

  # invoked from /plus
  def create_checkout
    session = Stripe::Checkout::Session.create({
      ui_mode: 'embedded',
      customer: current_user.stripe_customer_id,
      allow_promotion_codes: true,
      payment_method_types: ['card'],
      line_items: [{
        price: Rails.application.credentials.stripe.plus_subscription_price_id,
        quantity: current_user.devices.count
      }],
      mode: 'subscription', # use 'payment' for products with 1-time pricing
      return_url: "#{Rails.application.credentials.base_url}#{new_billing_portal_path}?session_id={CHECKOUT_SESSION_ID}",
      automatic_tax: { enabled: false }
    })

    { clientSecret: session.client_secret }
  end

  # invoked from /account when user is already subscribed
  def modify_subscription
    session = Stripe::BillingPortal::Session.create({
      customer: current_user.stripe_customer_id,
      return_url: "#{Rails.application.credentials.base_url}#{account_index_path}?updated=true"
    })

    session.url
  end
end
