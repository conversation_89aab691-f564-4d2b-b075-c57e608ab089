class PlusController < ApplicationController
  before_action :authenticate_user!
  before_action :prevent_unauthorized_subscription
  before_action :prevent_double_subscription

  def index; end

  private

  def prevent_unauthorized_subscription
    redirect_to account_index_path, alert: "You can't do that (yet)" unless Flipper.enabled?(:plus_subscription, current_user)
  end

  def prevent_double_subscription
    redirect_to account_index_path, alert: "You're already subscribed" if current_user.subscribed_to_plus?
  end
end
