# frozen_string_literal: true

class StripeCustomerWorker
  include Sidekiq::Worker
  sidekiq_options retry: false, queue: :critical

  def perform(user_id)
    user = User.find(user_id)

    customer = Stripe::Customer.create({
      email: user.email,
      name: user.full_name,
      metadata: {
        external_id: user.id
      }
    })

    user.update_attribute(:stripe_customer_id, customer.id)
  end
end
