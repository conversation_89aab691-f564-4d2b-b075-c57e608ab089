# frozen_string_literal: true

class StripeSubscriptionWorker
  include Sidekiq::Worker
  sidekiq_options retry: false

  def perform(user_id)
    user = User.find(user_id)
    subscription = user.current_stripe_subscription

    # cancel subscriptions after their scheduled end date passes
    # user with stripe_subscription_id=nil may re-subscribe
    if subscription&.cancel_at && Time.at(subscription.cancel_at) < Time.now
      user.update(stripe_subscription_id: nil)
    end
  end
end
