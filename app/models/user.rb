class User < ApplicationRecord
  include Billable
  include Flipper::Identifier
  include Signupable

  has_many :devices, dependent: :destroy
  has_many :mashups
  has_many :oauth_token_stores
  has_many :playlist_items, through: :devices
  has_many :plugins
  has_many :plugin_settings
  has_many :screens, dependent: :destroy
  has_many :usage_transactions

  normalizes :locale, with: ->(locale) { locale.empty? ? nil : locale }, apply_to_nil: false

  scope :referral_partners, -> { where("referral_code IS NOT NULL and referral_code_external_id IS NOT NULL") }

  before_create :opt_in_to_schedules_2_beta
  before_save :clean_flipper_groups

  # :nocov:
  def self.ransackable_attributes(*)
    ["admin", "created_at", "credentials", "email", "tz", "first_name", "last_name", "locale", "encrypted_password", "id", "id_value", "remember_created_at", "reset_password_sent_at", "reset_password_token", "updated_at", "discord_username"]
  end

  def self.find_by_name_or_email_cont(name)
    where(
      "first_name ilike :q or last_name ilike :q or email ilike :q",
      { q: "%#{name}%" }
    )
  end

  def self.ransackable_scopes(*)
    %i[find_by_name_or_email_cont]
  end

  def self.ransackable_associations(*)
    %i[find_by_name_or_email_cont]
  end

  def self.generate_otp_reset
    SecureRandom.alphanumeric(6).upcase
  end
  # :nocov:

  def current_device
    @current_device ||= devices.find_by_id(current_device_id) || devices.first
  end

  # rubocop:disable Naming/AccessorMethodName
  def set_current_device(device_id)
    update(current_device_id: device_id)
  end
  # rubocop:enable Naming/AccessorMethodName

  def full_name
    "#{first_name} #{last_name}"
  end

  def as_json(_options = nil)
    {
      name: full_name,
      email: email,
      first_name: first_name,
      last_name: last_name,
      locale: locale || 'en',
      time_zone: tz,
      time_zone_iana: ActiveSupport::TimeZone::MAPPING[tz],
      utc_offset: Time.now.in_time_zone(tz).utc_offset
    }
  end

  def owns_developer_edition_device?
    devices.where.not(developer_edition_unlocked_at: nil).exists?
  end

  def hackathon_user?
    return false if devices.empty?

    devices.filter(&:hackathon_device?) == devices
  end

  # helper link for Intercom portal sidebar
  def impersonation_link = "#{Rails.application.credentials.base_url}/admin/users/#{id}/impersonate"

  def referral_partner? = referral_code? && referral_code_external_id?

  def datetime_now = datetime_at(DateTime.now)

  def datetime_at(time)
    time.in_time_zone(tz)
  rescue ArgumentError
    time.in_time_zone("Eastern Time (US & Canada)")
  end

  def reset_api_key!
    # key should not have hyphens, to make it easier to copy/paste
    self.update(api_key: "user_#{SecureRandom.base36(24)}")
  end

  def flipper_id = "user_#{id}"

  # TODO: Remove after migration is complete
  def migrate_to_singular_groups
    devices.each do |device|
      print "Device ##{device.id}..."

      default_group = device.playlist_groups[0]
      if default_group.nil?
        puts 'no playlist groups found, skipping'
        next
      end

      device.playlist_groups.includes(:playlist_items).each do |pg|
        print "PlaylistGroup ##{pg.id}..."

        # convert pg.start_time and end_time (minutes since midnight) to time string
        # e.g. 540 becomes "09:00"
        start_hour, start_minute = pg.start_time.divmod(60)
        end_hour, end_minute = pg.end_time.divmod(60)

        start_time_str = format('%<h>02d:%<m>02d', h: start_hour, m: start_minute)
        end_time_str = format('%<h>02d:%<m>02d', h: end_hour, m: end_minute)

        # special case: extend end-of-day to 23:59
        end_time_str = '23:59' if end_time_str == '23:45'

        # only set a custom schedule if not "all day"
        priority = PlaylistItem::NORMAL_PRIORITY
        schedule = if start_time_str == '00:00' && end_time_str == '23:59'
                     print 'all day...'
                     PlaylistSchedule.new
                   else
                     print 'custom schedule...'
                     priority = PlaylistItem::IMPORTANT_PRIORITY
                     PlaylistSchedule.new(
                       week_schedules: [
                         WeekSchedule.new(week_days: (0..6).to_a, start_time: start_time_str, end_time: end_time_str)
                       ]
                     )
                   end

        print 'updating schedules...'
        pg.playlist_items.update_all(duration: pg.refresh_interval, schedule:, priority:)
      end

      print 'moving items...'
      device.playlist_items.update_all(playlist_group_id: default_group.id)
      device.playlist_groups.where.not(id: default_group.id).delete_all
      Device.reset_counters(device.id, :playlist_groups_count)
      default_group.update!(start_time: 0, end_time: (23 * 60) + 45) # all day
      puts 'done.'
    end

    true
  end

  def clean_flipper_groups
    self.flipper_groups = flipper_groups.reject(&:blank?).uniq
  end

  def opt_in_to_schedules_2_beta
    self.flipper_groups << 'schedules_2_beta'
  end
end
