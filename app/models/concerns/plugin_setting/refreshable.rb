class PluginSetting
  module Refreshable
    extend ActiveSupport::Concern

    REFRESH_OPTIONS = {
      '1x /day' => 1440,
      '2x /day' => 720,
      '3x /day' => 480,
      '4x /day' => 360,
      'Every 4 hrs' => 240,
      'Every 2 hrs' => 120,
      'Hourly' => 60,
      'Every 30 mins' => 30,
      'Every 15 mins' => 15,
      'Every 10 mins' => 10,
      'Every 5 mins' => 5
    }.freeze

    SLOW_REFRESH_OPTIONS = REFRESH_OPTIONS.select { it.include?('day') }.freeze

    included do
      scope :sort_by_refresh_at, -> { where(refresh_at: ..1.minutes.from_now).order(refresh_at: :asc) }
      attribute :refresh_interval, default: 1440
    end

    class_methods do
      def claim_refresh_tasks(plugin_id, limit, params = {})
        plugin_where_string = "plugin_id = #{plugin_id}"
        plugin_where_string = "plugin_id IN (#{plugin_id.join(',')})" if plugin_id.is_a?(Array)

        plugin_where_string += if params[:priority]
                                 " AND refresh_interval = 5"
                               else
                                 " AND refresh_interval > 5"
                               end

        sql = <<~SQL
          UPDATE plugin_settings
          SET job_claimed = true
          WHERE id IN (
            SELECT id FROM plugin_settings
            WHERE
              #{plugin_where_string} AND state != 2 AND (playlists_count > 0 OR mashup_contents_count > 0) AND job_claimed = false AND
              (refresh_at IS NULL OR refresh_at <= NOW() + interval '1 minute')
            ORDER BY refresh_at ASC NULLS FIRST
            LIMIT #{limit}
            FOR UPDATE SKIP LOCKED
          )
          RETURNING *
        SQL

        ActiveRecord::Base.uncached do
          find_by_sql(sql)
        end
      end

      def claim_refresh_task(plugin_setting_id)
        sql = <<~SQL
          UPDATE plugin_settings
          SET job_claimed = true
          WHERE id IN (
            SELECT id FROM plugin_settings
            WHERE
              id = #{plugin_setting_id} AND job_claimed = false
            FOR UPDATE SKIP LOCKED
          )
          RETURNING *
        SQL

        ActiveRecord::Base.uncached do
          find_by_sql(sql)
        end.first
      end
    end

    def refresh!
      reset_persisted_locals!
      Plugins::ForceRefreshWorker.perform_async(id)
    end

    def set_refresh_at = update(refresh_at: DateTime.now)

    def refresh_in_24hr = update(refresh_at: DateTime.now + 1.day)

    def set_next_refresh_timestamp!(next_refresh = calculate_next_refresh)
      if next_refresh == REFRESH_OPTIONS['1x /day']
        next_refresh = (((user.datetime_now.beginning_of_day + 1455.minutes).to_i - DateTime.now.to_i) / 60).to_i
      end

      update_columns(
        refresh_at: next_refresh.minutes.from_now,
        previous_refresh_at: DateTime.now,
        job_claimed: false
      )
    end

    def calculate_next_refresh = [plugin.refresh_every.to_i, refresh_interval.to_i].max

    def refresh_options
      if user.subscribed_to_plus?
        REFRESH_OPTIONS
      else
        REFRESH_OPTIONS.reject { |_k, v| v < (plugin.refresh_every || 15) }
      end
    end
  end
end
