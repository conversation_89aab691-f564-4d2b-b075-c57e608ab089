module Signupable
  extend ActiveSupport::Concern

  included do
    # Include default devise modules. Others available are:
    # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
    # devise :database_authenticatable, :registerable, :recoverable, :rememberable
    # validates :email, uniqueness: { case_sensitive: false }, presence: true

    devise :two_factor_authenticatable
    devise :registerable, :recoverable, :rememberable
    validates :email, uniqueness: { case_sensitive: false }, presence: true

    after_commit :process_callbacks, on: :create
  end

  def process_callbacks
    send_welcome_email
    create_marketing_profile
    setup_stripe_customer
    check_against_blacklist
  end

  def send_welcome_email
    UserMailer.welcome(self.id).deliver_later
  end

  def create_marketing_profile
    return if Rails.env.test?

    MarketingProfileWorker.perform_async(id)
  end

  def check_against_blacklist
    blacklist_str = Setting.get('user_email_blacklist')
    blacklist = blacklist_str.split(',').map(&:squish)

    DiscordWorker.perform_async("Block this device or request payment: #{email}") if blacklist.find { it == email }
  end
end
