module Billable
  extend ActiveSupport::Concern

  included do
    scope :subscribed, -> { where.not(stripe_subscription_id: [nil, '']) }
  end

  def subscribed_to_plus?
    stripe_subscription_id?
  end

  def setup_stripe_customer
    return if Rails.env.test?

    return if stripe_customer_id?

    StripeCustomerWorker.perform_async(id)
  end

  def set_stripe_subscription
    subscription_id = current_stripe_subscription&.id
    update(stripe_subscription_id: subscription_id)
  end

  def current_stripe_subscription
    stripe_subscriptions&.first
  end

  def stripe_subscriptions
    stripe_customer.subscriptions
  end

  def stripe_customer
    Stripe::Customer.retrieve({
      id: stripe_customer_id,
      expand: ['subscriptions']
    })
  end
end
