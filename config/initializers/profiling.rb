# Profiling Configuration for TRMNL Image Generation Pipeline
#
# This initializer sets up profiling configuration for the image generation pipeline.
# Profiling can be enabled/disabled via environment variables or Rails configuration.

Rails.application.configure do
  # Enable profiling in development by default, or when explicitly enabled
  config.profiling_enabled = ENV['TRMNL_PROFILING_ENABLED'] == 'true' || Rails.env.development?
  
  # Configure profiling output directory
  config.profiling_output_dir = Rails.root.join('tmp', 'profiles')
  
  # Ensure profiling directory exists
  FileUtils.mkdir_p(config.profiling_output_dir) if config.profiling_enabled
  
  # Log profiling status
  if config.profiling_enabled
    Rails.logger.info "[PROFILING] Image pipeline profiling enabled"
    Rails.logger.info "[PROFILING] Profile output directory: #{config.profiling_output_dir}"
    Rails.logger.info "[PROFILING] View profiles at: https://vernier.prof/"
  end
end

# Add profiling configuration to environment variables documentation
if Rails.env.development?
  puts <<~PROFILING_INFO
    
    🔍 TRMNL Image Pipeline Profiling
    ================================
    
    Profiling Status: #{Rails.application.config.profiling_enabled ? '✅ ENABLED' : '❌ DISABLED'}
    
    To enable profiling:
      export TRMNL_PROFILING_ENABLED=true
    
    To run benchmarks:
      TRMNL_PROFILING_ENABLED=true ruby misc/benchmarking/image_pipeline_profiler.rb
      TRMNL_PROFILING_ENABLED=true ruby misc/benchmarking/component_benchmarks.rb
    
    Profile output: #{Rails.application.config.profiling_output_dir}
    View profiles: https://vernier.prof/
    
  PROFILING_INFO
end
