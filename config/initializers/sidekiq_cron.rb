plugins = YAML.load_file("db/data/plugins.yml").as_json

sidekiq_cron_jobs = [
  # { name: 'PriorityPluginProcessorWorker', class: 'Plugins::PluginProcessorWorker', cron: "*/1 * * * *", args: [37, priority: true], queue: :priority },
  { name: 'MashupProcessorWorker', class: 'Plugins::MashupProcessorWorker', cron: "*/30 * * * * *" }, # once every 30 seconds.
  # { name: 'PriorityMashupProcessorWorker', class: 'Plugins::MashupProcessorWorker', cron: "*/1 * * * *", args: { priority: true }, queue: :priority }, # once every minute.
  { name: 'PublicPluginProcessorWorker', class: 'Plugins::PublicPluginProcessorWorker', cron: "*/1 * * * *" },
  { name: 'Statuspage Worker', class: 'StatuspageWorker', cron: "* * * * *" },
  { name: 'StripeSubscriptionWorker', class: 'StripeSubscriptionWorker', cron: "0 0 * * *" } # once daily, removes canceled subscriptions
]

# Global plugins (Process image once, and show same content for all users, eg: Product Hunt, HackerNews)
sidekiq_cron_jobs << plugins.filter { |p| p['active'] && p['global'] }.map do |plugin|
  { name: plugin['keyname'], class: 'Plugins::GlobalPluginProcessorWorker', cron: plugin['cron'], args: plugin['id'] }
end

# Native Plugins
sidekiq_cron_jobs << plugins.filter { |p| p['active'] && !p['global'] }.map do |plugin|
  { name: plugin['keyname'], class: 'Plugins::PluginProcessorWorker', cron: plugin['cron'], args: plugin['id'] }
end

# Sleep Workers
sidekiq_cron_jobs << { name: 'PrepareDeviceBeforeWakeupWorker', class: 'Devices::WakeupFromSleepWorker', cron: "*/10 * * * *" }
sidekiq_cron_jobs << { name: 'ScreenDisplayCounterIncrement', class: 'ScreenDisplayCounterIncrementWorker', cron: "*/1 * * * *" }

# Credit Usage Related worker
sidekiq_cron_jobs << { name: 'CreateCreditsWorker', class: 'Users::CreateCreditsWorker', cron: "1 0 1 * *" } # 1st of every month at 00:01
sidekiq_cron_jobs << { name: 'RegisterDebitWorker', class: 'Users::RegisterDebitWorker', cron: "1 0 * * *" } # every day at 00:01

if Rails.env.production?
  Sidekiq::Cron::Job.load_from_array! sidekiq_cron_jobs.flatten
end

# WARNING
# When changing the name of the job, the old job doesn't get deleted and still executes till eternity,
# More info: https://github.com/sidekiq-cron/sidekiq-cron/issues/267
# Delete it manually using Sidekiq::Cron::Job.find('name').destroy
# Eg: Sidekiq::Cron::Job.find('custom_plugin').destroy
