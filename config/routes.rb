Rails.application.routes.draw do
  mount Rswag::Ui::Engine => '/api-docs'
  mount Rswag::Api::Engine => '/api-docs'
  ActiveAdmin.routes(self)
  root 'pages#home'

  devise_for :users, path: '', path_names: { sign_in: 'login', sign_up: 'signup' }, controllers: { registrations: 'registrations', sessions: 'sessions' }

  # accessible from /account page; produces QR + confirms OTP was created successfully
  get 'enable_otp', to: 'users#enable_otp', as: 'enable_otp'
  post 'enable_otp', to: 'users#enable_otp_verify', as: 'enable_otp_verify'

  # 2-legged login step following successful authentication
  get 'users/otp', to: 'users#show_otp', as: 'user_otp'
  post 'users/otp', to: 'users#verify_otp', as: 'user_otp_verify'

  patch 'users/reset_otp', to: 'users#reset_otp', as: 'user_otp_reset'

  get 'logout', to: 'pages#logout', as: 'logout'

  # Healthcheck endpoint for load balancer
  get 'up', to: 'pages#up', as: 'up'

  namespace :api do
    scope module: 'device' do
      # kindle image proxy
      post 'kindle/proxy_image', to: 'kindle#proxy_image', as: :kindle_proxy

      # Get the currently displayed screen (currently used for Chrome Extension)
      resources :current_screen, only: [:index]

      # device fetches this endpoint for content
      resources :display, only: [:index] do # un-versioned for all-gen compatibility
        collection do
          get :test # prototype / dev testing from Raspberry Pi firmware
        end
      end

      # device pings this endpoint during setup (following wifi credential swap)
      resources :setup, only: [:index]

      # device sends error content here when display fetch fails
      resources :log, only: [:create]
    end

    scope module: 'user' do
      resources :devices, only: [:index, :show]

      resources :plugin_settings, only: [:create, :destroy] do
        resource :data, only: [:show], module: :plugin_settings
        resource :archive, only: [:show, :create], module: :plugin_settings
      end

      namespace :playlists do
        resources :items, only: [:index, :update]
      end
    end

    # where self-hosted OSS customers can retrieve latest firmware
    resources :firmware, only: [] do
      get :latest, on: :collection
    end

    resources :custom_plugins, only: [:show] do
      member do
        post :create
      end
    end

    resources :plugin_settings, only: [] do
      collection do
        post :xhr_select
        post :xhr_select_search
        post :xhr_function
      end
    end

    resources :partners, only: [:create]
    resources :bounties, only: [:index]
  end

  namespace :admin_api, module: 'api/admin' do
    resources :devices, only: [:show, :create]
    resources :smiirl, only: [:index]
    resources :referral_orders, only: [:create]
    resources :reports, only: [:create]
  end

  namespace :feeds do
    resources :announcements, only: :index
    resources :posts, only: :index
  end

  resources :account, only: %i[index update] do
    get :stop_impersonating, on: :collection
    post :reset_api_key, on: :collection
    post :join_schedule_beta, on: :collection
  end

  resources :blog_posts, except: [:destroy], controller: :blog_posts, path: 'blog', param: :slug

  resources :dashboard, only: [:index]
  resources :news, only: [:index, :show]
  resources :devices, only: [:index, :edit, :update, :new] do
    member do
      post :switch
      post :identify
      post :dissociate
      post :mirror
      post :resync_mirror
      post :stop_mirror

      get 'kindle/TRMNL_KINDLE', to: 'devices/kindle#generate_zip', as: :kindle_zip
    end
    post :associate, on: :collection

    resources :logs, only: [:index, :destroy]
  end

  resources :mashups, only: [:create, :edit]
  resources :mashup_contents, only: [:create]

  resources :oauth do
    post :token, on: :collection
  end

  resources :playlists, controller: :playlist_items, only: [:index, :destroy] do
    collection do
      patch :set_preferences
    end

    member do
      resource :schedule, only: [:update] do
        member do
          patch :preview
        end
      end

      patch :sort
      patch :toggle_visibility
      post :duplicate
    end
  end

  resources :playlist_groups, only: [:create, :update]

  resources :plugin_settings, only: [:index, :new, :edit, :create, :update, :destroy] do
    collection do
      get :redirect
    end
    collection do
      resource :custom_fields do
        collection do
          post :verify
        end
      end
    end
    member do
      resource :markup, only: [:edit, :update] do
        collection do
          post :preview
          get :preview
          get :previewer
        end
      end
      match :redirect, via: [:get, :post]
      get :reinit
      post :reset_credentials
      post :force_refresh
      post :add_to_playlist
      post :copy
      post :enable_debug_logs
      get :reset_health
      get :export
      get :configure
    end
  end

  resources :referral_code, only: [:index, :create]

  scope module: "plugins" do
    resources :recipes, only: [:new, :index, :show] do
      member do
        match :install, via: [:get, :post]
        get :install_read_only
        post :submit
      end
    end
    resources :plugins, only: [:index, :show] do
      get :demo, on: :collection
      get :multi, on: :collection
      get :v2, on: :member

      collection do
        resources :my, as: 'my_plugin', except: [:destroy] do
          post 'submit_for_review', on: :member
        end
      end
    end
  end

  resources :upgrade, only: [:index, :create] do
    get 'confirm', on: :collection
  end

  resources :plus, only: [:index]
  resources :billing_portal, only: [:new, :create]

  resources :virtual_devices, only: [:create]
  resources :missing_devices, only: [:create]
  resources :hackathon_entries, only: [:create] # TODO: :show for judges

  # static pages - consider adding new content to sitemap.rb generator
  pages = %w[about bounties brand buy roadmap m2 custom_plugin_example_data privacy terms developers hackathon welcome third_party_plugin_example_install]
  post 'custom_plugin_example_xhr_select', to: 'pages#custom_plugin_example_xhr_select'
  get 'flash', to: 'pages#flash_firmware', as: 'flash'
  get 'fortune-500', to: 'pages#fortune_500', as: 'fortune_500'
  get 'claim-a-device', to: 'pages#claim_a_device', as: 'claim_a_device'
  get 'find-my-device', to: 'pages#find_my_device', as: 'find_my_device'

  pages.each do |page|
    get "/#{page}", to: "pages##{page}", as: page.gsub('-', '_')
  end
  resources :integrations, only: [:index, :show]

  resources :checkouts, only: [:create]

  get 'guides/:handle', to: 'guides#show', as: 'guide'
  resources :guides, only: [:show]
  resources :redirect_urls, only: [:show], controller: :redirect_urls, path: 'go', param: :path

  # terminalwire CLI for generating plugins
  match "/terminal", to: Terminalwire::Server::Thor.new(MainTerminal), via: [:get, :connect]

  # admin panels
  authenticated :user, lambda(&:admin?) do
    post '/admin/guides/:guide_id/ai_generate_description', to: 'guides#ai_generate_description'

    namespace :admin do
      if defined?(Sidekiq)
        require 'sidekiq/web'
        mount Sidekiq::Web => '/sidekiq'
      end

      mount Flipper::UI.app(Flipper) => '/flipper'

      resources :stats, only: [:index]
    end
  end

  scope :framework do
    get '/', to: 'framework_legacy#index', as: :framework_legacy

    components = %w[background image border view layout title_bar columns grid title description
                    label value table chart item gap clamp overflow format_value fit_value content_limiter flex size text spacing mashup pixel_perfect rich_text text_stroke image_stroke]

    components.each do |component|
      get "/#{component}", to: "framework_legacy##{component}", as: "framework_legacy_#{component}"
    end
  end

  scope :framework_v2 do
    get '/', to: 'framework#index', as: :framework

    components = %w[background image border rounded view layout title_bar columns grid title description
                    label value table chart item progress gap clamp overflow format_value fit_value content_limiter flex size responsive text spacing mashup pixel_perfect rich_text text_stroke image_stroke visibility divider]

    components.each do |component|
      get "/#{component}", to: "framework##{component}", as: "framework_#{component}"
    end
  end

  scope :design do
    get '/', to: 'design#index', as: :design
    get 'logo', to: 'design#logo', as: :design_logo
    get 'colors', to: 'design#colors', as: :design_colors
  end

  scope :byod do
    # Installer script routes - with environment param optional
    get 'install-trmnl-display', to: 'trmnl_display#installer_script'
    get 'install-trmnl-display/:environment', to: 'trmnl_display#installer_script'
  end

  get "/.well-known/jwks.json", to: 'wellknown#jwks'

  get "/chrome", to: 'chrome#show'
  get "/chrome/privacy", to: 'chrome#privacy'
  get "/chrome/google49c8f7c152568de2.html", to: 'chrome#show'
end
