puts "Generating announcements..."
announcements = [
  {
    title: 'Introducing Our New App!',
    body: 'Discover exciting features and enhancements that are new with this update.',
    created_at: Date.parse('2024-04-05T00:00:00')
  },
  {
    title: 'Download Now and Explore',
    body: 'Get the app on your device and start exploring. There are new features to check out!',
    created_at: Date.parse('2024-04-06T00:00:00')
  },
  {
    title: 'Behind the Scenes',
    body: 'Learn about the tech stack and engineering that went into creating this product.',
    created_at: Date.parse('2024-04-07T00:00:00')
  }
]
announcements.each { |attributes| Announcement.find_or_create_by! attributes } if Announcement.count.zero?

puts "syncing plugins..."
system('bin/rails plugins:sync')

# Need this to run tests, as these are constant data. Do not delete.
[
  {
    id: 1,
    keyname: 'og',
    name: 'TRMNL OG',
    width: 800,
    height: 480,
    colour_depth: 1,
    colours: 2,
    scale_factor: 1,
    format: 'bmp3'
  },
  {
    id: 2,
    keyname: 'v2',
    name: 'TRMNL Pro',
    width: 1872,
    height: 1404,
    colour_depth: 4,
    colours: 16,
    scale_factor: 1,
    format: 'png'
  },
  {
    id: 3,
    keyname: 'og_png',
    name: 'TRMNL OG',
    width: 800,
    height: 480,
    colour_depth: 1,
    colours: 2,
    format: 'png'
  },
  {
    id: 4,
    keyname: 'amazon_kindle_2024',
    name: 'Amazon Kindle 2024',
    width: 1400,
    height: 840,
    colour_depth: 8,
    colours: 256,
    scale_factor: 1,
    rotate: 90,
    offset_x: 75,
    offset_y: 25,
    format: 'png',
    device_type: :kindle
  },
  {
    id: 5,
    keyname: 'amazon_kindle_paperwhite_6th_gen',
    name: 'Amazon Kindle PW 6th Gen',
    width: 1024,
    height: 768,
    colour_depth: 8,
    colours: 256,
    scale_factor: 1,
    rotate: 90,
    offset_x: 0,
    offset_y: 0,
    format: 'png',
    device_type: :kindle
  },
  {
    id: 6,
    keyname: 'amazon_kindle_paperwhite_7th_gen',
    name: 'Amazon Kindle PW 7th Gen',
    width: 1448,
    height: 1072,
    colour_depth: 8,
    colours: 256,
    scale_factor: 1,
    rotate: 90,
    offset_x: 0,
    offset_y: 0,
    format: 'png',
    device_type: :kindle
  },
  {
    id: 7,
    keyname: 'inkplate_10',
    name: 'Inkplate 10',
    width: 1200,
    height: 820,
    colour_depth: 3,
    colours: 8,
    scale_factor: 1,
    rotate: 0,
    offset_x: 0,
    offset_y: 0,
    format: 'png',
    device_type: :byod
  },
  {
    id: 8,
    keyname: 'amazon_kindle_7',
    name: 'Amazon Kindle 7',
    width: 800,
    height: 600,
    colour_depth: 8,
    colours: 256,
    scale_factor: 1,
    rotate: 90,
    offset_x: 0,
    offset_y: 0,
    format: 'png',
    device_type: :kindle
  },
  {
    id: 9,
    keyname: 'mac_classic',
    name: 'Classic Macintosh',
    width: 512,
    height: 342,
    colour_depth: 1,
    colours: 2,
    scale_factor: 1,
    rotate: 0,
    offset_x: 0,
    offset_y: 0,
    format: 'bmp3',
    device_type: :byod
  },
  {
    id: 10,
    keyname: 'playdate',
    name: 'Playdate',
    width: 450, # TODO: supposed to be 400, but Firefox can't go that narrow
    height: 240,
    colour_depth: 1,
    colours: 2,
    scale_factor: 1,
    rotate: 0,
    offset_x: 0,
    offset_y: 0,
    format: 'png',
    device_type: :byod
  },
  {
    id: 11,
    keyname: 'inky_impression_7_3',
    name: 'Inky Impression 7.3',
    width: 800,
    height: 480,
    colour_depth: 8,
    colours: 7,
    scale_factor: 1,
    rotate: 0,
    offset_x: 0,
    offset_y: 0,
    format: 'png',
    device_type: :byod
  },
  {
    id: 12,
    keyname: 'kobo_libra_2',
    name: 'Kobo Libra 2',
    width: 1680,
    height: 1264,
    colour_depth: 8,  # 8-bit framebuffer (standard on modern E-Ink readers)
    colours: 256,     # 2^8 greyscale steps
    scale_factor: 1,
    rotate: 90,       # match typical landscape capture like the Kindle example
    offset_x: 0,
    offset_y: 0,
    format: 'png',
    device_type: :byod
  },
  {
    id: 13,
    keyname: 'amazon_kindle_oasis_2',
    name: 'Amazon Kindle Oasis 2',
    width: 1680,
    height: 1264,
    colour_depth: 8,
    colours: 256,
    scale_factor: 1,
    rotate: 90,
    offset_x: 0,
    offset_y: 0,
    format: 'png',
    device_type: :byod
  },
  {
    id: 14,
    keyname: 'og_plus',
    name: 'TRMNL OG+',
    width: 800,
    height: 480,
    colour_depth: 2,
    colours: 4,
    scale_factor: 1,
    format: 'png'
  },
  {
    id: 15,
    keyname: 'kobo_aura_one',
    name: 'Kobo Aura One',
    width: 1872,
    height: 1404,
    colour_depth: 8,
    colours: 256,
    scale_factor: 1,
    rotate: 90,
    offset_x: 0,
    offset_y: 0,
    format: 'png',
    device_type: :byod
  }
].each do |model|
  DeviceModel.upsert(model, unique_by: :keyname)
end
