# AGENT.md

## Commands
- **Test**: `bundle exec rspec` (all tests), `bundle exec rspec spec/path/to/test_spec.rb` (single file), `bundle exec rspec spec/path/to/test_spec.rb:42` (specific line)
- **Lint**: `bundle exec rubocop` (check), `bundle exec rubocop -a` (auto-fix)
- **Setup**: `bin/setup` (initial), `bin/dev` (server + sidekiq), `bin/rails server` (server only)
- **Console**: `bin/rails console`
- **Database**: `bin/rails db:migrate`, `bin/rails db:reset`

## Architecture
Ruby on Rails 8.0.2 app for e-ink displays. Core models: Device (physical display), Plugin (integration), PluginSetting (user config), Playlist (content schedule), Screen (rendered image). Plugins in `services/plugins/` inherit from base Plugin class. Background jobs via Sidekiq. APIs at `/api/v1/` (device) and `/api/v2/` (REST).

## Code Style
- Ruby 3.4, RuboCop for linting (config in `.rubocop.yml`)
- Models in `app/models/`, services in `services/`, jobs in `app/jobs/`
- Plugin naming: `PluginNamePlugin` class, `plugin_name_plugin.rb` file
- Tests use RSpec, FactoryBot, VCR; located in `spec/`
- Views use Tailwind CSS, Stimulus.js, ViewComponent
- Environment variables for config (Redis, PostgreSQL, S3, Stripe, OAuth)
- Plugin development: `rails g plugin PluginName`, implement `render` method, add view template
- Database: PostgreSQL, all times UTC, encrypted OAuth tokens in plugin_settings
