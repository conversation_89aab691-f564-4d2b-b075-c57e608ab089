require 'rails_helper'

RSpec.describe Mashup, type: :model do
  subject { build(:mashup) }

  it 'has a valid factory' do
    expect(subject).to be_valid
  end

  describe 'ActiveModel validations' do
    it { expect(subject).to validate_presence_of(:layout) }
  end

  describe 'ActiveModel associations' do
    it { expect(subject).to have_one(:screen) }
    it { expect(subject).to have_many(:playlist_items) }
    it { expect(subject).to have_many(:mashup_contents) }
    it { expect(subject).to belong_to(:user) }
  end

  describe 'class methods' do
    describe '#claim_refresh_tasks' do
      let!(:mashup) { create(:mashup) }
      let!(:mashup2) { create(:mashup) }

      context 'normal queue' do
        it 'returns refresh tasks' do
          expect(Mashup.claim_refresh_tasks(10)).to eq([mashup, mashup2])
        end

        it 'returns empty array' do
          Mashup.update_all(refresh_interval: 5)
          expect(Mashup.claim_refresh_tasks(10)).to eq([])
        end
      end

      context 'priority queue' do
        it 'returns empty array' do
          expect(Mashup.claim_refresh_tasks(10, priority: true)).to eq([])
        end

        it 'returns refresh tasks' do
          Mashup.update_all(refresh_interval: 5)
          expect(Mashup.claim_refresh_tasks(10, priority: true)).to match_array([mashup, mashup2])
        end
      end
    end

    describe '#claim_refresh_task' do
      let(:mashup) { create(:mashup) }
      it 'claims a refresh task just once' do
        expect(Mashup.claim_refresh_task(mashup.id)).to eq(mashup)
        expect(Mashup.claim_refresh_task(mashup.id)).to eq(nil)
      end
    end
  end

  describe 'instance methods' do
    let(:plugin) { Plugin.find_by_keyname('days_left_year') }
    let(:plugin_setting) { create(:plugin_setting, plugin_id: plugin.id, refresh_interval: 30) }

    before do
      subject.save
    end

    describe '#full_layout?' do
      it 'determines if mashup should just be a regular playlist' do
        expect(subject.full_layout?).to be false
        subject.update(layout: '1x1')
        expect(subject.full_layout?).to be true
      end
    end

    describe '#contents_required' do
      it 'returns content required' do
        expect(subject.contents_required).to eql 2
      end
    end

    describe '#mashup_complete?' do
      it 'returns false when insufficient child mashup contents exist' do
        expect(subject.mashup_complete?).to be false
      end

      it 'returns true when sufficient child mashup contents exist' do
        positions = MashupContent::POSITIONS.dup

        subject.contents_required.times do
          create(:mashup_content, mashup: subject, position: positions.shift, plugin_setting_id: plugin_setting.id)
        end

        expect(subject.mashup_complete?).to be true
      end
    end

    describe '#set_next_refresh_timestamp!' do
      before do
        Timecop.freeze(Date.today.beginning_of_day)
        positions = MashupContent::POSITIONS.dup
        subject.contents_required.times do
          create(:mashup_content, mashup: subject, position: positions.shift, plugin_setting_id: plugin_setting.id)
        end
      end

      context 'mashup has refresh_interval of 30 minutes' do
        it 'should set next refresh_at timestamp' do
          expect(subject.set_next_refresh_timestamp!).to eq(true)
          expect(subject.refresh_at.to_datetime.utc.strftime("%Y-%m-%d %I:%M:%S")).to eq((DateTime.now + 30.minutes).utc.strftime("%Y-%m-%d %I:%M:%S"))
        end
      end

      context 'mashup has refresh_interval of 1 day (1440 minutes)' do
        before do
          plugin_setting.update(refresh_interval: 1440) # update the refresh_interval to 1 day
          subject.reload
          subject.update_refresh_interval!
        end

        it 'should set next refresh_at timestamp' do
          expect(subject.set_next_refresh_timestamp!).to eq(true)
          expect(subject.refresh_at.to_datetime).to eq(subject.user.datetime_now.beginning_of_day + 1470.minutes) # refreshes at 00:30 localtime.
        end
      end

      context 'mashup has refresh_interval of 15 seconds' do
        before do
          plugin_setting.update(refresh_interval: 15) # update the refresh_interval to 15 minutes
          subject.reload
          subject.update_refresh_interval!
        end
        it 'should set next refresh_at timestamp' do
          expect(subject.set_next_refresh_timestamp!).to eq(true)
          expect(subject.refresh_at.to_datetime.utc.strftime("%Y-%m-%d %I:%M:%S")).to eq((DateTime.now + 15.minutes).utc.strftime("%Y-%m-%d %I:%M:%S"))
        end
      end
    end

    describe '#update_refresh_interval!' do
      before do
        Timecop.freeze(Date.today.beginning_of_day)

        positions = MashupContent::POSITIONS.dup
        subject.contents_required.times do
          create(:mashup_content, mashup: subject, position: positions.shift, plugin_setting_id: plugin_setting.id)
        end
      end

      context 'mashup_incomplete?' do
        before do
          subject.mashup_contents.destroy_all
          subject.reload
          subject.update_refresh_interval!
          subject.reload
        end

        it 'refresh_interval is set to 1440' do
          expect(subject.refresh_interval).to eq(plugin_setting.refresh_interval)
          expect(subject.refresh_at).to be_between(0.minutes.from_now, 5.minutes.from_now)
        end
      end

      context 'if plugin_settings has refresh_interval nil' do
        before do
          plugin_setting.update(refresh_interval: nil)
          subject.reload
          subject.update_refresh_interval!
          subject.reload
        end

        it 'refresh_interval is set to 360' do
          expect(subject.refresh_interval).to eq(360) # plugin#refresh_every
          expect(subject.refresh_at).to be_between(0.minutes.from_now, 5.minutes.from_now)
        end
      end

      context 'if plugin_settings has refresh_interval 720 minutes but plugin#refresh_every is 60 minutes' do
        before do
          plugin_setting.update(refresh_interval: 720)
          plugin_setting.plugin.update(refresh_every: 60)
          subject.reload
          subject.update_refresh_interval!
          subject.reload
        end

        it 'refresh_interval is set to 720' do
          expect(subject.refresh_interval).to eq(720)
          expect(subject.refresh_at).to be_between(0.minutes.from_now, 5.minutes.from_now)
        end
      end

      context 'if plugin_settings has refresh_interval 10 minutes' do
        before do
          plugin_setting.update(refresh_interval: 10)
          subject.reload
          subject.update_refresh_interval!
          subject.reload
        end

        it 'refresh_interval is set to 15' do
          expect(subject.refresh_interval).to eq(15)
          expect(subject.refresh_at).to be_between(0.minutes.from_now, 5.minutes.from_now)
        end
      end
    end

    describe '#screen_image' do
      it 'returns screen url if present' do
        screen = create(:screen, mashup: subject)
        expect(subject.screen_image).to eq(screen.image)
      end

      it 'returns placeholder image if no screen attached' do
        expect(subject.screen_image).to include("_tags.svg")
      end
    end

    describe '#skip_display?' do
      pending 'returns true if all plugin_settings skip_display? is true' do
        plugin_setting.update(skip_display: true)
        subject.mashup_contents << create(:mashup_content, mashup: subject, plugin_setting: plugin_setting)
        expect(subject.skip_display?).to be true
      end

      it 'returns false if any plugin_settings skip_display? is false' do
        subject.mashup_contents << create(:mashup_content, mashup: subject, plugin_setting: plugin_setting)
        expect(subject.skip_display?).to be false
      end
    end
  end
end
