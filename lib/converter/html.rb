require 'base64'

require_relative 'firefox_pool'
require_relative 'chrome_pool'
require_relative 'magick_wrapper'
require_relative '../profiling/performance_profiler'

module Converter
  class Html
    include Preprocessor
    include MagickWrapper
    include Profiling::PerformanceProfiler

    PROCESSING_TIMEOUT = 120.freeze
    def initialize(params)
      self.input = params[:html]
      self.image = params[:image]
      self.processor = params[:processor] || 'firefox'
      self.plugin = params[:plugin] || 'Mashup'
      self.id = params[:id]
      self.model_id = params[:model_id] || 3 # default to OG (PNG)
      self.output = Pathname.new("/tmp/screenshot-#{Date.today}-#{SecureRandom.hex(4)}.png")
      self.render_skipped = false # changes to true if window.TRMNL_SKIP_SCREEN_GENERATION is set in the page
      self.display_skipped = false # changes to true if window.TRMNL_SKIP_DISPLAY is set in the page
    end

    attr_accessor :input, :output, :image, :processor, :plugin, :id, :model_id, :display_skipped, :render_skipped

    def process
      profile_comprehensive("html-conversion-#{processor}-#{plugin}-#{id}") do
        Timeout.timeout(PROCESSING_TIMEOUT) do
          time_operation("preprocessors") { run_preprocessors }

          time_operation("browser-conversion") do
            process_by_firefox? ? convert_to_image_by_firefox : convert_to_image_by_chrome
          end

          if output.file?
            time_operation("image-processing") do
              process_as_image? ? monochrome_image : monochrome
            end
          end
          output
        end
      end
    rescue Timeout::Error => e
      Rails.logger.info "ERROR: Plugins::Base.process! -> Timeout::ExitException -> #{plugin} #{id} #{e.message}"
    end

    private

    def process_by_firefox? = processor == 'firefox'

    def process_as_image? = image || input.include?('image-dither')

    def convert_to_image_by_firefox
      profile_browser_operation('firefox', 'convert-to-image') do
        window = nil

        Timeout.timeout(60) do
          # Check out a window from the pool
          window = time_operation("firefox-pool-checkout") do
            FirefoxPool.instance.check_out_window
          end

          # Activate the window and load the initial content
          time_operation("firefox-content-loading") do
            window.with_driver do |driver|
              driver.navigate.to("about:blank")

              window.set_viewport_size!(width, height, scale_factor)

              # Load in the content
              driver.execute_script(<<~JS, input, css_style_override)
                // Set content HTML
                document.open();
                document.write(arguments[0]);
                document.close();

                // Override :root CSS style
                const style = document.createElement('style');
                style.innerHTML = arguments[1];
                document.head.appendChild(style);

                // Hide scrollbars
                document.body.style.overflow = "hidden";
                document.documentElement.style.overflow = "hidden";
              JS
            end
          end

          # Each driver has only one active window, so we need to release the driver so that other
          # windows can be made active for processing in parallel. Now, we periodically poll the driver to
          # check if this window has finished loading before taking the screenshot.
          #
          # Start with an initial wait for the page to load
          sleep 1.0

          time_operation("firefox-screenshot-capture") do
            done = false
            loop do
              # Activate the window and attempt to take a screenshot
              window.with_driver do |driver|
                highcharts_done = evaluate_js(driver, "window.TRMNL_HIGHCHARTS_DONE == true")
                document_ready = evaluate_js(driver, "document.readyState == 'complete'")

                if highcharts_done && document_ready
                  self.render_skipped = should_skip_render?(driver)
                  self.display_skipped = should_skip_display?(driver)

                  driver.save_screenshot(output) unless render_skipped || display_skipped

                  done = true
                end
              end

              break if done

              # Randomize sleep to spread out demand for window switching (average 1 sec)
              sleep 0.75 + Random.rand(0.0..0.5)
            end
          end
        end
      end
    rescue Selenium::WebDriver::Error::TimeoutError, Timeout::Error => e
      # If a timeout occurred, assume the browser is bogged down, so kill the window
      window&.destroy
      window = nil

      # Re-raise the error
      raise e
    ensure
      window&.check_in
    end

    def convert_to_image_by_chrome
      profile_browser_operation('chrome', 'convert-to-image') do
        browser = time_operation("chrome-pool-checkout") do
          ChromePool.instance.check_out_browser
        end

        context = browser.driver.contexts.create
        page = context.create_page

        time_operation("chrome-content-loading") do
          page.set_viewport(width: width, height: height, scale_factor: scale_factor)
          page.content = input
          page.add_style_tag(content: css_style_override)

          page.network.blacklist = [/nr-data\.net/, /www\.googleadservices\.com/, /adtrafficquality\.google/, /doubleclick\.net/,
                                    /googlesyndication\.com/, /googletagmanager\.com/, /stripe\.com/, /pay\.google\.com/, /blob:null/]

          wait_for_stop_loading(page)
        end

        time_operation("chrome-screenshot-capture") do
          self.render_skipped = should_skip_render?(page)
          self.display_skipped = should_skip_display?(page)
          page.screenshot(path: output, format: :png) unless render_skipped || display_skipped
        end

        context.dispose
      end
    rescue Ferrum::TimeoutError => e
      Rails.logger.info "ERROR -> Converter::Html#convert_to_image_by_chrome -> #{e.message}"
      context.dispose
      # browser.restart
    rescue Ferrum::DeadBrowserError, Ferrum::ProcessTimeoutError, Ferrum::NoSuchTargetError
      browser.restart
    end

    # Overall at max wait for 3 seconds
    def wait_for_stop_loading(page)
      count = 0

      while page.frames.first.state != :stopped_loading && count < 30
        pending = page.network.traffic.select { it.pending? && !it.url&.include?('dithering') && !it.url&.start_with?("blob:") }
        highcharts_done = evaluate_js(page, "window.TRMNL_HIGHCHARTS_DONE == true")
        break if highcharts_done && pending.count.zero? # wait till pending traffic count becomes zero.

        count += 1
        sleep 0.1
      end
    end

    def should_skip_render?(page) = evaluate_js(page, "window.TRMNL_SKIP_SCREEN_GENERATION == true")
    def should_skip_display?(page) = evaluate_js(page, "window.TRMNL_SKIP_DISPLAY == true")

    def evaluate_js(page, expression)
      if process_by_firefox?
        page.execute_script("return #{expression}")
      else
        page.evaluate(expression)
      end
    end

    def css_style_override = ":root { --screen-w: #{width}px; --screen-h: #{height}px; }"

    def output_path = output.to_s

    # rubocop:disable Style/OpenStructUse
    def model = @model ||= OpenStruct.new(DeviceModel.cached_find(model_id))
    # rubocop:enable Style/OpenStructUse

    delegate  :width, :height, :colour_depth, :format, :colours, :scale_factor, :rotate, to: :model
  end
end
