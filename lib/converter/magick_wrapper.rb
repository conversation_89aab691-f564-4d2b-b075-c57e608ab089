require_relative '../profiling/performance_profiler'

module Converter
  module MagickWrap<PERSON>
    include Profiling::PerformanceProfiler
    def monochrome
      profile_imagemagick_operation("monochrome-#{colour_depth}bit") do
        retry_count = 0
        begin
          MiniMagick.convert do |m|
            m << output_path
            m.rotate(rotate) if rotate.positive?
            colour_depth == 1 ? m.monochrome : m.type('Grayscale') # use monochrome filter for 1 bit image and Grayscale for others.
            m.colors(colours) # Specify the number of colours,
            m.depth(colour_depth) # Should be set to 1 for 1-bit output
            m.strip # Remove any additional metadata
            m << ("#{format}:" << output_path) # file format bmp3/png etc
          end
        rescue MiniMagick::TimeoutError, MiniMagick::Error, MiniMagick::Invalid => e
          retry_count += 1
          sleep retry_count
          retry if retry_count <= 1
          Rails.logger.info "ERROR -> Converter::Html#monochrome -> #{plugin} #{id} #{e.message} IMAGE_VALID: #{MiniMagick::Image.new(output_path).valid?}"
        end
      end
    end

    def monochrome_image
      profile_imagemagick_operation("monochrome-image-#{colour_depth}bit") do
        retry_count = 0
        # Changelog:
        # ImageMagick 6.XX used to convert the png to bitmap with dithering while maintaining the channel to 1
        # The same seems to be broken with imagemagick 7.XX
        # So in order to reduce the channel from 8 to 1, I just rerun the command, and it's working
        # TODO for future, find a better way to generate image screens.
        begin
          MiniMagick.convert do |m|
            m << output_path
            m.rotate(rotate) if rotate.positive?
            if colour_depth == 1
              m.dither << 'FloydSteinberg'
              m.remap << 'pattern:gray50'
              m.depth(colour_depth)
            elsif colour_depth > 1 && colour_depth < 8
              m.type('Grayscale')
              m.auto_level
              m.normalize
              m.level('5%,95%')
              m.ordered_dither("o8x8,#{colours}")
              m.colors(colours)
              m.depth(colour_depth)
            elsif colour_depth == 8
              m.type('Grayscale')
              m.depth(8)
            end
            m.strip # Remove any additional metadata
            m << ("#{format}:" << output_path) # Converts to Bitmap.
          end
        rescue MiniMagick::TimeoutError, MiniMagick::Error, MiniMagick::Invalid => e
          retry_count += 1
          sleep retry_count
          retry if retry_count <= 1
          Rails.logger.info "ERROR -> Converter::Html#monochrome_image -> #{plugin} #{id} #{e.message} IMAGE_VALID: #{MiniMagick::Image.new(output_path).valid?}"
        end
      end
    end

    def monochrome_image_4_bit
      profile_imagemagick_operation("monochrome-image-4bit") do
        MiniMagick.convert do |m|
          m << output_path
          m.type('GrayScale')
          m.auto_level
          m.normalize
          m.level('5%,95%')
          m.ordered_dither('o8x8,16')
          m.colors(16) # Reduce the number of colors to 16 (4-bit)
          m.depth(4)
          m.strip
          m << ('bmp3:' << output_path) # Converts to Bitmap.
        end
      end
    end
  end
end
