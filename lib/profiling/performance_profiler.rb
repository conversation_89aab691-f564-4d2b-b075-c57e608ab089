require 'vernier'
require 'benchmark'

module Profiling
  module PerformanceProfiler
    extend ActiveSupport::Concern

    class_methods do
      def profiling_enabled?
        ENV['TRMNL_PROFILING_ENABLED'] == 'true' || Rails.env.development?
      end

      def profile_output_dir
        @profile_output_dir ||= Rails.root.join('tmp', 'profiles').tap do |dir|
          FileUtils.mkdir_p(dir) unless dir.exist?
        end
      end
    end

    # Profile a block of code with Vernier and return the result
    def profile_with_vernier(name, **options, &block)
      return yield unless self.class.profiling_enabled?

      timestamp = Time.current.to_i
      output_file = self.class.profile_output_dir.join("#{name}-#{timestamp}.json")
      
      log_profiling("Starting Vernier profile: #{name}")
      start_time = Time.current
      
      result = Vernier.profile(out: output_file.to_s, **options, &block)
      
      elapsed = Time.current - start_time
      log_profiling("Vernier profile completed: #{name} (#{elapsed.round(3)}s) -> #{output_file}")
      log_profiling("View at: https://vernier.prof/ (upload #{output_file})")
      
      result
    end

    # Time a block of code and log the results
    def time_operation(name, level: :info, &block)
      start_time = Time.current
      result = yield
      elapsed = Time.current - start_time
      
      log_profiling("#{name}: #{elapsed.round(3)}s", level)
      result
    end

    # Profile memory usage of a block
    def profile_memory(name, &block)
      return yield unless self.class.profiling_enabled?

      require 'benchmark/memory'
      
      log_profiling("Starting memory profile: #{name}")
      
      report = Benchmark.memory do |x|
        x.report(name, &block)
      end
      
      log_profiling("Memory profile: #{name}")
      log_profiling("  Allocated: #{report.entries.first.measurement.allocated}")
      log_profiling("  Retained: #{report.entries.first.measurement.retained}")
      
      yield
    end

    # Comprehensive profiling that includes timing, memory, and Vernier
    def profile_comprehensive(name, include_memory: false, **vernier_options, &block)
      return yield unless self.class.profiling_enabled?

      log_profiling("=== Starting comprehensive profile: #{name} ===")
      
      if include_memory
        profile_memory("#{name}-memory") do
          profile_with_vernier("#{name}-vernier", **vernier_options) do
            time_operation("#{name}-timing", &block)
          end
        end
      else
        profile_with_vernier("#{name}-vernier", **vernier_options) do
          time_operation("#{name}-timing", &block)
        end
      end
    end

    # Profile browser operations specifically
    def profile_browser_operation(browser_type, operation_name, &block)
      profile_name = "browser-#{browser_type}-#{operation_name}"
      profile_comprehensive(profile_name, &block)
    end

    # Profile ImageMagick operations specifically  
    def profile_imagemagick_operation(operation_name, &block)
      profile_name = "imagemagick-#{operation_name}"
      profile_comprehensive(profile_name, &block)
    end

    private

    def log_profiling(message, level = :info)
      if defined?(Rails)
        Rails.logger.send(level, "[PROFILING] #{message}")
      else
        puts "[PROFILING] #{message}"
      end
    end
  end
end
