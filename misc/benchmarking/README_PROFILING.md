# TRMNL Image Pipeline Profiling

This directory contains comprehensive profiling tools for the TRMNL image generation pipeline. Use these tools to identify performance bottlenecks and optimize the image conversion process.

## 🚀 Quick Start

### Enable Profiling
```bash
export TRMNL_PROFILING_ENABLED=true
```

### Run Quick Profile
```bash
# Test Chrome with OG model (PNG)
ruby misc/benchmarking/quick_profile.rb chrome 3

# Test Firefox with OG+ model (BMP)  
ruby misc/benchmarking/quick_profile.rb firefox 4
```

### Run Full Pipeline Benchmark
```bash
TRMNL_PROFILING_ENABLED=true ruby misc/benchmarking/image_pipeline_profiler.rb
```

## 📊 Available Tools

### 1. Quick Profile (`quick_profile.rb`)
**Purpose**: Fast profiling of a single image generation operation  
**Use Case**: Development and debugging

```bash
ruby misc/benchmarking/quick_profile.rb [browser] [model_id]

# Examples:
ruby misc/benchmarking/quick_profile.rb chrome 3    # Chrome + OG PNG
ruby misc/benchmarking/quick_profile.rb firefox 4   # Firefox + OG+ BMP
```

### 2. Full Pipeline Profiler (`image_pipeline_profiler.rb`)
**Purpose**: Comprehensive analysis of the entire image generation pipeline  
**Use Case**: Performance optimization and comparison

```bash
TRMNL_PROFILING_ENABLED=true ruby misc/benchmarking/image_pipeline_profiler.rb
```

**Tests Include**:
- Browser comparison (Firefox vs Chrome)
- Image processing methods (standard vs image-dither)
- Device model performance (1-bit, 4-bit, 8-bit)
- Content complexity impact
- Memory usage analysis

### 3. Component Benchmarks (`component_benchmarks.rb`)
**Purpose**: Focused benchmarks for individual components  
**Use Case**: Identifying specific bottlenecks

```bash
TRMNL_PROFILING_ENABLED=true ruby misc/benchmarking/component_benchmarks.rb
```

**Components Tested**:
- Browser pool operations (checkout/checkin)
- ImageMagick operations (rotation, dithering, color depth)
- Content loading performance
- Screenshot operations

## 🔍 Understanding Results

### Vernier Profiles
When profiling is enabled, detailed flame graphs are generated:

1. **Location**: `tmp/profiles/*.json`
2. **Viewer**: Upload to [https://vernier.prof/](https://vernier.prof/)
3. **Analysis**: Look for:
   - Wide bars (time-consuming operations)
   - Deep stacks (complex call chains)
   - Repeated patterns (optimization opportunities)

### Timing Logs
All tools provide detailed timing information in the console:

```
[PROFILING] browser-conversion: 2.341s
[PROFILING] image-processing: 0.892s
[PROFILING] imagemagick-monochrome-1bit: 0.756s
```

### Service Layer Logs
Enhanced logging in `Plugins::Base#process!`:

```
Screen generated in 3.234s (setup: 0.012s, processing: 3.156s), uploading now
Upload completed in 0.066s
```

## 🎯 Common Bottlenecks

### Browser Operations
- **Pool checkout/checkin**: Should be < 0.1s
- **Content loading**: Varies by complexity (0.5-3s)
- **Screenshot capture**: Should be < 0.5s

### ImageMagick Processing
- **1-bit monochrome**: 0.5-2s depending on image size
- **4-bit grayscale**: 0.3-1s
- **8-bit grayscale**: 0.2-0.8s
- **Dithering**: Adds 0.2-1s depending on algorithm

### Memory Usage
- **Typical allocation**: 50-200MB per conversion
- **Retained memory**: Should be minimal
- **Watch for**: Memory leaks in browser pools

## 🛠️ Optimization Strategies

### 1. Browser Selection
- **Chrome**: Generally faster for simple content
- **Firefox**: More stable for complex content
- **Test both**: Performance varies by content type

### 2. Content Optimization
- **Reduce complexity**: Fewer DOM elements = faster rendering
- **Optimize CSS**: Avoid complex gradients and animations
- **Image optimization**: Pre-optimize images before conversion

### 3. ImageMagick Tuning
- **Batch operations**: Combine multiple operations in single command
- **Algorithm selection**: Choose appropriate dithering for quality/speed trade-off
- **Memory limits**: Configure ImageMagick memory limits

### 4. Caching Strategies
- **Content-based caching**: Hash HTML content to avoid regeneration
- **Image caching**: Cache processed images for identical content
- **Pool optimization**: Tune browser pool sizes

## 📈 Performance Targets

### Acceptable Performance
- **Total pipeline**: < 5s for typical content
- **Browser conversion**: < 3s
- **ImageMagick processing**: < 2s
- **Upload**: < 0.5s

### Excellent Performance
- **Total pipeline**: < 3s
- **Browser conversion**: < 2s
- **ImageMagick processing**: < 1s
- **Upload**: < 0.2s

## 🔧 Configuration

### Environment Variables
```bash
# Enable profiling
export TRMNL_PROFILING_ENABLED=true

# Custom profile output directory (optional)
export TRMNL_PROFILE_OUTPUT_DIR=/custom/path/profiles
```

### Rails Configuration
```ruby
# config/environments/development.rb
config.profiling_enabled = true
config.profiling_output_dir = Rails.root.join('tmp', 'profiles')
```

## 📝 Adding Custom Profiling

### In Your Code
```ruby
class MyClass
  include Profiling::PerformanceProfiler

  def my_method
    profile_comprehensive("my-operation") do
      # Your code here
    end
  end

  def quick_timing
    time_operation("quick-check") do
      # Your code here
    end
  end
end
```

### Available Methods
- `profile_comprehensive(name, **options)` - Full profiling with Vernier
- `time_operation(name, level: :info)` - Simple timing
- `profile_memory(name)` - Memory usage analysis
- `profile_browser_operation(browser, operation)` - Browser-specific profiling
- `profile_imagemagick_operation(operation)` - ImageMagick-specific profiling

## 🐛 Troubleshooting

### Common Issues

1. **No profiles generated**
   - Check `TRMNL_PROFILING_ENABLED=true`
   - Verify `tmp/profiles` directory exists
   - Check file permissions

2. **Browser pool errors**
   - Ensure browsers are installed
   - Check browser pool configuration
   - Restart browser pools if needed

3. **ImageMagick errors**
   - Verify ImageMagick installation
   - Check memory limits
   - Ensure input files exist

### Debug Mode
```bash
# Enable debug logging
RAILS_LOG_LEVEL=debug TRMNL_PROFILING_ENABLED=true ruby misc/benchmarking/quick_profile.rb
```

## 📚 Additional Resources

- [Vernier Documentation](https://github.com/jhawthorn/vernier)
- [ImageMagick Performance Tips](https://imagemagick.org/script/architecture.php#performance)
- [Chrome DevTools Protocol](https://chromedevtools.github.io/devtools-protocol/)
- [Firefox WebDriver](https://firefox-source-docs.mozilla.org/testing/geckodriver/)

---

💡 **Pro Tip**: Start with `quick_profile.rb` for immediate feedback, then use the full pipeline profiler for comprehensive analysis.
