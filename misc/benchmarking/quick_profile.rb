#!/usr/bin/env ruby

# Quick Profile Script
# 
# A simple script to quickly profile a single image generation operation
# for immediate feedback during development.
#
# Usage:
#   ruby misc/benchmarking/quick_profile.rb [browser] [model_id]
#   
# Examples:
#   ruby misc/benchmarking/quick_profile.rb chrome 3
#   ruby misc/benchmarking/quick_profile.rb firefox 4

require_relative '../../config/environment'

class QuickProfiler
  include Profiling::PerformanceProfiler

  def initialize(browser = 'chrome', model_id = 3)
    @browser = browser
    @model_id = model_id.to_i
    @test_html = generate_simple_test_html
  end

  def run_quick_profile
    puts "🚀 Quick Profile: #{@browser.capitalize} + Model #{@model_id}"
    puts "=" * 50

    if self.class.profiling_enabled?
      run_with_profiling
    else
      run_with_timing_only
    end

    puts "\n✅ Quick profile completed!"
    
    if self.class.profiling_enabled?
      puts "📁 Profile saved to: #{self.class.profile_output_dir}"
      puts "🔍 View at: https://vernier.prof/"
    else
      puts "💡 Enable detailed profiling with: TRMNL_PROFILING_ENABLED=true"
    end
  end

  private

  def run_with_profiling
    puts "📊 Running with detailed Vernier profiling..."
    
    profile_comprehensive("quick-profile-#{@browser}-model#{@model_id}", include_memory: true) do
      run_conversion
    end
  end

  def run_with_timing_only
    puts "⏱️  Running with timing only..."
    
    time_operation("quick-profile-#{@browser}-model#{@model_id}") do
      run_conversion
    end
  end

  def run_conversion
    converter = Converter::Html.new({
      html: @test_html,
      processor: @browser,
      plugin: 'quick-profile',
      id: "quick-test-#{Time.current.to_i}",
      model_id: @model_id
    })

    result = converter.process
    
    if result&.file?
      file_size = File.size(result)
      puts "📄 Generated image: #{result} (#{file_size} bytes)"
      
      # Clean up
      File.delete(result) if File.exist?(result)
    else
      puts "⚠️  No image file generated"
    end

    result
  end

  def generate_simple_test_html
    <<~HTML
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Quick Profile Test</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }
          .container { 
            max-width: 600px; 
            margin: 0 auto; 
            text-align: center; 
          }
          .metric { 
            background: rgba(255,255,255,0.1); 
            padding: 20px; 
            margin: 10px 0; 
            border-radius: 10px; 
          }
          .value { 
            font-size: 2em; 
            font-weight: bold; 
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>Performance Test Dashboard</h1>
          <div class="metric">
            <div class="value">#{rand(100)}%</div>
            <div>CPU Usage</div>
          </div>
          <div class="metric">
            <div class="value">#{rand(1000)}ms</div>
            <div>Response Time</div>
          </div>
          <div class="metric">
            <div class="value">#{rand(50)}GB</div>
            <div>Storage Used</div>
          </div>
          <p>Generated at: #{Time.current.strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        <script>
          // Simulate some processing time
          setTimeout(() => {
            window.TRMNL_HIGHCHARTS_DONE = true;
          }, 100);
        </script>
      </body>
      </html>
    HTML
  end
end

# Parse command line arguments
browser = ARGV[0] || 'chrome'
model_id = ARGV[1] || '3'

# Validate browser
unless ['chrome', 'firefox'].include?(browser.downcase)
  puts "❌ Invalid browser: #{browser}"
  puts "💡 Use 'chrome' or 'firefox'"
  exit 1
end

# Validate model ID
unless model_id.match?(/^\d+$/)
  puts "❌ Invalid model ID: #{model_id}"
  puts "💡 Use a numeric model ID (e.g., 3, 4, 8, 9)"
  exit 1
end

# Run the quick profiler
profiler = QuickProfiler.new(browser.downcase, model_id)
profiler.run_quick_profile
