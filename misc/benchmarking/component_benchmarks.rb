#!/usr/bin/env ruby

# Component-Specific Benchmarks
# 
# This script provides focused benchmarks for individual components
# of the image generation pipeline to identify specific bottlenecks.
#
# Usage:
#   TRMNL_PROFILING_ENABLED=true ruby misc/benchmarking/component_benchmarks.rb

require_relative '../../config/environment'
require 'benchmark'
require 'benchmark/ips' if defined?(Benchmark::IPS)

class ComponentBenchmarks
  include Profiling::PerformanceProfiler

  def initialize
    @test_image_path = create_test_image
    setup_test_data
  end

  def run_all_benchmarks
    puts "🔧 Component-Specific Benchmarks"
    puts "=" * 50

    benchmark_browser_pools
    benchmark_imagemagick_operations
    benchmark_content_loading
    benchmark_screenshot_operations
    
    cleanup
  end

  private

  def benchmark_browser_pools
    puts "\n🌐 Browser Pool Performance"
    puts "-" * 30

    # Firefox Pool Benchmarks
    puts "Firefox Pool Operations:"
    time_operation("firefox-pool-initialization") do
      # Test pool initialization overhead
      pool = Converter::FirefoxPool.new(1, 1)
      pool.instance_variable_get(:@browsers).each(&:quit) rescue nil
    end

    if firefox_pool_available?
      benchmark_firefox_operations
    else
      puts "⚠️  Firefox pool not available"
    end

    # Chrome Pool Benchmarks  
    puts "\nChrome Pool Operations:"
    time_operation("chrome-pool-initialization") do
      # Test pool initialization overhead
      pool = Converter::ChromePool.new(1)
      pool.instance_variable_get(:@browsers).each(&:quit) rescue nil
    end

    if chrome_pool_available?
      benchmark_chrome_operations
    else
      puts "⚠️  Chrome pool not available"
    end
  end

  def benchmark_firefox_operations
    puts "Testing Firefox operations..."
    
    # Checkout/Checkin performance
    time_operation("firefox-checkout-checkin") do
      window = Converter::FirefoxPool.instance.check_out_window
      window.check_in
    end

    # Content loading performance
    time_operation("firefox-content-loading") do
      window = Converter::FirefoxPool.instance.check_out_window
      begin
        window.with_driver do |driver|
          driver.navigate.to("data:text/html,<html><body><h1>Test</h1></body></html>")
        end
      ensure
        window.check_in
      end
    end
  end

  def benchmark_chrome_operations
    puts "Testing Chrome operations..."
    
    # Browser checkout performance
    time_operation("chrome-checkout") do
      browser = Converter::ChromePool.instance.check_out_browser
      # No explicit checkin needed for Chrome
    end

    # Context creation performance
    time_operation("chrome-context-creation") do
      browser = Converter::ChromePool.instance.check_out_browser
      context = browser.driver.contexts.create
      context.dispose
    end

    # Page creation and content loading
    time_operation("chrome-page-operations") do
      browser = Converter::ChromePool.instance.check_out_browser
      context = browser.driver.contexts.create
      page = context.create_page
      page.content = "<html><body><h1>Test</h1></body></html>"
      context.dispose
    end
  end

  def benchmark_imagemagick_operations
    puts "\n🎨 ImageMagick Operations"
    puts "-" * 30

    return unless File.exist?(@test_image_path)

    # Test different ImageMagick operations individually
    operations = [
      { name: 'rotation', depth: 1 },
      { name: 'monochrome-1bit', depth: 1 },
      { name: 'grayscale-4bit', depth: 4 },
      { name: 'grayscale-8bit', depth: 8 }
    ]

    operations.each do |op|
      benchmark_imagemagick_operation(op[:name], op[:depth])
    end

    # Benchmark dithering algorithms
    benchmark_dithering_methods
  end

  def benchmark_imagemagick_operation(operation_name, color_depth)
    puts "Testing #{operation_name}..."
    
    time_operation("imagemagick-#{operation_name}") do
      output_path = "/tmp/benchmark-#{operation_name}-#{Time.current.to_i}.png"
      
      begin
        MiniMagick.convert do |m|
          m << @test_image_path
          
          case operation_name
          when 'rotation'
            m.rotate(90)
          when 'monochrome-1bit'
            m.monochrome
            m.depth(1)
          when 'grayscale-4bit'
            m.type('Grayscale')
            m.colors(16)
            m.depth(4)
          when 'grayscale-8bit'
            m.type('Grayscale')
            m.depth(8)
          end
          
          m.strip
          m << ("png:" << output_path)
        end
      ensure
        File.delete(output_path) if File.exist?(output_path)
      end
    end
  end

  def benchmark_dithering_methods
    puts "Testing dithering methods..."
    
    dithering_methods = [
      'FloydSteinberg',
      'o8x8,16',
      'o4x4,16'
    ]

    dithering_methods.each do |method|
      time_operation("dithering-#{method.gsub(',', '-')}") do
        output_path = "/tmp/benchmark-dither-#{Time.current.to_i}.png"
        
        begin
          MiniMagick.convert do |m|
            m << @test_image_path
            
            if method == 'FloydSteinberg'
              m.dither << method
              m.remap << 'pattern:gray50'
            else
              m.ordered_dither(method)
            end
            
            m.depth(4)
            m.strip
            m << ("png:" << output_path)
          end
        ensure
          File.delete(output_path) if File.exist?(output_path)
        end
      end
    end
  end

  def benchmark_content_loading
    puts "\n📄 Content Loading Performance"
    puts "-" * 30

    content_sizes = [
      { name: 'small', size: 1_000 },      # 1KB
      { name: 'medium', size: 10_000 },    # 10KB  
      { name: 'large', size: 100_000 }     # 100KB
    ]

    content_sizes.each do |test|
      html_content = generate_html_content(test[:size])
      
      time_operation("content-loading-#{test[:name]}") do
        # Test with Chrome (faster for this benchmark)
        browser = Converter::ChromePool.instance.check_out_browser
        context = browser.driver.contexts.create
        page = context.create_page
        page.content = html_content
        
        # Wait for content to load
        count = 0
        while page.frames.first.state != :stopped_loading && count < 30
          count += 1
          sleep 0.1
        end
        
        context.dispose
      end
    end
  end

  def benchmark_screenshot_operations
    puts "\n📸 Screenshot Operations"
    puts "-" * 30

    viewport_sizes = [
      { name: 'standard', width: 800, height: 480 },
      { name: 'large', width: 1200, height: 720 },
      { name: 'small', width: 400, height: 240 }
    ]

    viewport_sizes.each do |size|
      time_operation("screenshot-#{size[:name]}") do
        output_path = "/tmp/benchmark-screenshot-#{Time.current.to_i}.png"
        
        begin
          browser = Converter::ChromePool.instance.check_out_browser
          context = browser.driver.contexts.create
          page = context.create_page
          page.set_viewport(width: size[:width], height: size[:height], scale_factor: 1)
          page.content = "<html><body><h1>Screenshot Test</h1></body></html>"
          page.screenshot(path: output_path, format: :png)
          context.dispose
        ensure
          File.delete(output_path) if File.exist?(output_path)
        end
      end
    end
  end

  def create_test_image
    # Create a test PNG image for ImageMagick benchmarks
    test_path = "/tmp/benchmark-test-image.png"
    
    MiniMagick.convert do |m|
      m.size('800x480')
      m.canvas('white')
      m.fill('black')
      m.gravity('center')
      m.pointsize(48)
      m.annotate('+0+0', 'Test Image')
      m << ("png:" << test_path)
    end
    
    test_path
  rescue => e
    puts "⚠️  Could not create test image: #{e.message}"
    nil
  end

  def generate_html_content(target_size)
    base_content = "<html><body><h1>Performance Test</h1>"
    
    # Add content to reach target size
    paragraph = "<p>This is test content for performance benchmarking. " * 10 + "</p>"
    paragraphs_needed = [1, (target_size - base_content.length) / paragraph.length].max
    
    content = base_content
    paragraphs_needed.times { content += paragraph }
    content += "</body></html>"
    
    content
  end

  def setup_test_data
    # Ensure browser pools are initialized
    begin
      Converter::ChromePool.instance
    rescue => e
      puts "⚠️  Chrome pool initialization failed: #{e.message}"
    end

    begin
      Converter::FirefoxPool.instance
    rescue => e
      puts "⚠️  Firefox pool initialization failed: #{e.message}"
    end
  end

  def firefox_pool_available?
    Converter::FirefoxPool.instance.instance_variable_get(:@browsers).any?
  rescue
    false
  end

  def chrome_pool_available?
    Converter::ChromePool.instance.instance_variable_get(:@browsers).any?
  rescue
    false
  end

  def cleanup
    # Clean up test files
    File.delete(@test_image_path) if @test_image_path && File.exist?(@test_image_path)
    Dir.glob('/tmp/benchmark-*.png').each { |f| File.delete(f) rescue nil }
    
    puts "\n✅ Component benchmarks completed!"
    puts "💡 Use the timing data above to identify bottlenecks in specific components."
  end
end

# Run the benchmarks if this script is executed directly
if __FILE__ == $0
  benchmarks = ComponentBenchmarks.new
  benchmarks.run_all_benchmarks
end
