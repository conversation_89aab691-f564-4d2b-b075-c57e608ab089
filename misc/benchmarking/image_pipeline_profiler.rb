#!/usr/bin/env ruby

# Image Pipeline Profiler
# 
# This script provides comprehensive profiling for the TRMNL image generation pipeline.
# It tests various scenarios and generates detailed performance reports.
#
# Usage:
#   TRMNL_PROFILING_ENABLED=true ruby misc/benchmarking/image_pipeline_profiler.rb
#
# View Vernier profiles at: https://vernier.prof/

require_relative '../../config/environment'
require 'benchmark'
require 'vernier'

class ImagePipelineProfiler
  include Profiling::PerformanceProfiler

  def initialize
    @results = {}
    @test_html = generate_test_html
    setup_output_directory
  end

  def run_all_benchmarks
    puts "🚀 Starting Image Pipeline Profiling..."
    puts "📊 Profiling enabled: #{self.class.profiling_enabled?}"
    puts "📁 Profile output: #{self.class.profile_output_dir}"
    puts "=" * 60

    benchmark_browser_comparison
    benchmark_image_processing_methods
    benchmark_device_models
    benchmark_content_complexity
    benchmark_memory_usage
    
    generate_summary_report
  end

  private

  def benchmark_browser_comparison
    puts "\n🌐 Browser Performance Comparison"
    puts "-" * 40

    browsers = ['firefox', 'chrome']
    browsers.each do |browser|
      puts "Testing #{browser.capitalize}..."
      
      result = profile_comprehensive("browser-comparison-#{browser}") do
        converter = Converter::Html.new({
          html: @test_html,
          processor: browser,
          plugin: 'benchmark',
          id: "browser-test-#{browser}",
          model_id: 3 # OG PNG
        })
        converter.process
      end
      
      @results["browser_#{browser}"] = result
      cleanup_temp_files
    end
  end

  def benchmark_image_processing_methods
    puts "\n🎨 Image Processing Methods"
    puts "-" * 40

    # Test different processing methods
    test_cases = [
      { name: 'standard', image: false },
      { name: 'image_dither', image: true }
    ]

    test_cases.each do |test_case|
      puts "Testing #{test_case[:name]} processing..."
      
      html_content = test_case[:image] ? @test_html.gsub('<body>', '<body class="image-dither">') : @test_html
      
      result = profile_comprehensive("image-processing-#{test_case[:name]}") do
        converter = Converter::Html.new({
          html: html_content,
          processor: 'chrome',
          plugin: 'benchmark',
          id: "processing-test-#{test_case[:name]}",
          model_id: 3,
          image: test_case[:image]
        })
        converter.process
      end
      
      @results["processing_#{test_case[:name]}"] = result
      cleanup_temp_files
    end
  end

  def benchmark_device_models
    puts "\n📱 Device Model Performance"
    puts "-" * 40

    # Test different device models (different color depths and formats)
    device_models = [
      { id: 3, name: 'OG (PNG)', depth: 1 },
      { id: 4, name: 'OG+ (BMP)', depth: 1 },
      { id: 8, name: 'Plus (4-bit)', depth: 4 },
      { id: 9, name: 'Plus+ (8-bit)', depth: 8 }
    ]

    device_models.each do |model|
      puts "Testing #{model[:name]} (#{model[:depth]}-bit)..."
      
      result = profile_comprehensive("device-model-#{model[:id]}") do
        converter = Converter::Html.new({
          html: @test_html,
          processor: 'chrome',
          plugin: 'benchmark',
          id: "model-test-#{model[:id]}",
          model_id: model[:id]
        })
        converter.process
      end
      
      @results["model_#{model[:id]}"] = result
      cleanup_temp_files
    end
  end

  def benchmark_content_complexity
    puts "\n📄 Content Complexity Impact"
    puts "-" * 40

    complexity_tests = [
      { name: 'simple', html: '<html><body><h1>Simple Test</h1></body></html>' },
      { name: 'medium', html: generate_medium_complexity_html },
      { name: 'complex', html: generate_complex_html }
    ]

    complexity_tests.each do |test|
      puts "Testing #{test[:name]} content..."
      
      result = profile_comprehensive("content-complexity-#{test[:name]}") do
        converter = Converter::Html.new({
          html: test[:html],
          processor: 'chrome',
          plugin: 'benchmark',
          id: "complexity-test-#{test[:name]}",
          model_id: 3
        })
        converter.process
      end
      
      @results["complexity_#{test[:name]}"] = result
      cleanup_temp_files
    end
  end

  def benchmark_memory_usage
    puts "\n💾 Memory Usage Analysis"
    puts "-" * 40

    return unless self.class.profiling_enabled?

    require 'benchmark/memory'

    puts "Analyzing memory usage for full pipeline..."
    
    report = Benchmark.memory do |x|
      x.report("full_pipeline") do
        converter = Converter::Html.new({
          html: @test_html,
          processor: 'chrome',
          plugin: 'benchmark',
          id: 'memory-test',
          model_id: 3
        })
        converter.process
        cleanup_temp_files
      end
    end

    puts "\nMemory Report:"
    puts "Allocated: #{report.entries.first.measurement.allocated}"
    puts "Retained: #{report.entries.first.measurement.retained}"
  end

  def generate_summary_report
    puts "\n📋 Performance Summary"
    puts "=" * 60

    if @results.any?
      puts "✅ Profiling completed successfully!"
      puts "📊 #{@results.size} test scenarios profiled"
      puts "📁 Profile files saved to: #{self.class.profile_output_dir}"
      puts "\n🔍 To analyze results:"
      puts "1. Visit https://vernier.prof/"
      puts "2. Upload the JSON files from #{self.class.profile_output_dir}"
      puts "3. Look for bottlenecks in the flame graphs"
      
      puts "\n📈 Quick Analysis Tips:"
      puts "- Compare browser performance (Firefox vs Chrome)"
      puts "- Check ImageMagick processing time vs browser time"
      puts "- Look for differences between device models"
      puts "- Identify content complexity impact"
    else
      puts "⚠️  No profiling data collected (profiling may be disabled)"
      puts "💡 Enable profiling with: TRMNL_PROFILING_ENABLED=true"
    end

    puts "\n🎯 Common Optimization Areas:"
    puts "- Browser pool checkout/checkin times"
    puts "- ImageMagick conversion operations"
    puts "- Content loading and rendering"
    puts "- Memory allocation patterns"
  end

  def generate_test_html
    <<~HTML
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>TRMNL Benchmark Test</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          .header { background: #333; color: white; padding: 10px; }
          .content { margin: 20px 0; }
          .chart { width: 100%; height: 200px; background: linear-gradient(45deg, #f0f0f0, #ddd); }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Performance Test Content</h1>
        </div>
        <div class="content">
          <p>This is test content for profiling the image generation pipeline.</p>
          <div class="chart"></div>
          <p>Testing various rendering scenarios and complexity levels.</p>
        </div>
        <script>
          window.TRMNL_HIGHCHARTS_DONE = true;
        </script>
      </body>
      </html>
    HTML
  end

  def generate_medium_complexity_html
    <<~HTML
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Medium Complexity Test</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
          .grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; }
          .card { border: 1px solid #ccc; padding: 15px; border-radius: 5px; }
          .chart { width: 100%; height: 150px; background: repeating-linear-gradient(45deg, #f0f0f0, #f0f0f0 10px, #e0e0e0 10px, #e0e0e0 20px); }
        </style>
      </head>
      <body>
        <h1>Medium Complexity Dashboard</h1>
        <div class="grid">
          #{(1..6).map { |i| "<div class='card'><h3>Widget #{i}</h3><div class='chart'></div><p>Data: #{rand(100)}</p></div>" }.join}
        </div>
        <script>window.TRMNL_HIGHCHARTS_DONE = true;</script>
      </body>
      </html>
    HTML
  end

  def generate_complex_html
    # Generate more complex HTML with tables, charts, and dynamic content
    <<~HTML
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Complex Dashboard Test</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 10px; font-size: 12px; }
          .dashboard { display: grid; grid-template-columns: repeat(4, 1fr); gap: 5px; }
          .widget { border: 1px solid #333; padding: 8px; }
          .chart { width: 100%; height: 80px; background: linear-gradient(to right, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7); }
          table { width: 100%; border-collapse: collapse; font-size: 10px; }
          th, td { border: 1px solid #ddd; padding: 2px; text-align: center; }
          .metric { font-size: 18px; font-weight: bold; color: #333; }
        </style>
      </head>
      <body>
        <h1>Complex Performance Dashboard</h1>
        <div class="dashboard">
          #{(1..12).map { |i| 
            "<div class='widget'><h4>Metric #{i}</h4><div class='metric'>#{rand(1000)}</div><div class='chart'></div></div>"
          }.join}
        </div>
        <table>
          <tr><th>Item</th><th>Value</th><th>Change</th><th>Status</th></tr>
          #{(1..10).map { |i| "<tr><td>Item #{i}</td><td>#{rand(100)}</td><td>+#{rand(10)}%</td><td>✓</td></tr>" }.join}
        </table>
        <script>window.TRMNL_HIGHCHARTS_DONE = true;</script>
      </body>
      </html>
    HTML
  end

  def setup_output_directory
    FileUtils.mkdir_p(self.class.profile_output_dir)
  end

  def cleanup_temp_files
    # Clean up any temporary screenshot files
    Dir.glob('/tmp/screenshot-*.png').each { |f| File.delete(f) rescue nil }
  end
end

# Run the profiler if this script is executed directly
if __FILE__ == $0
  profiler = ImagePipelineProfiler.new
  profiler.run_all_benchmarks
end
